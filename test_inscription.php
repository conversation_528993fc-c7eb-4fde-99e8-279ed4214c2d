<?php
require_once 'config.php';

echo "<h1>🧪 Test d'inscription automatique</h1>";

// Données de test pour l'inscription
$test_user = [
    'nom' => 'Test',
    'prenom' => 'Utilisateur',
    'email' => '<EMAIL>',
    'telephone' => '0612345678',
    'date_naissance' => '1995-05-15',
    'adresse' => '123 Rue de Test, Casablanca',
    'formation_souhaitee' => 'maintenance',
    'niveau_etude' => 'bac',
    'username' => 'test.user',
    'password' => 'password123'
];

echo "<h2>1. Vérification de la table users</h2>";
try {
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ Table users existe avec " . count($columns) . " colonnes</p>";
    
    // Vérifier les colonnes importantes
    $required_columns = ['nom', 'prenom', 'email', 'username', 'password', 'role', 'status'];
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            echo "<p style='color: green;'>✅ Colonne '$col' existe</p>";
        } else {
            echo "<p style='color: red;'>❌ Colonne '$col' manquante</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur table : " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. Test d'inscription simulée</h2>";

try {
    // Supprimer l'utilisateur de test s'il existe
    $stmt = $pdo->prepare("DELETE FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$test_user['username'], $test_user['email']]);
    echo "<p style='color: orange;'>🗑️ Utilisateur de test supprimé (s'il existait)</p>";
    
    // Simuler l'inscription
    $hashed_password = password_hash($test_user['password'], PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, 
                         formation_souhaitee, niveau_etude, username, password, role, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'student', 'active', NOW())
    ");
    
    $result = $stmt->execute([
        $test_user['nom'],
        $test_user['prenom'],
        $test_user['email'],
        $test_user['telephone'],
        $test_user['date_naissance'],
        $test_user['adresse'],
        $test_user['formation_souhaitee'],
        $test_user['niveau_etude'],
        $test_user['username'],
        $hashed_password
    ]);
    
    if ($result) {
        echo "<p style='color: green; font-weight: bold;'>✅ INSCRIPTION RÉUSSIE !</p>";
        
        // Vérifier que l'utilisateur a été créé
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$test_user['username']]);
        $created_user = $stmt->fetch();
        
        if ($created_user) {
            echo "<p style='color: green;'>✅ Utilisateur trouvé dans la base :</p>";
            echo "<ul>";
            echo "<li>ID : " . $created_user['id'] . "</li>";
            echo "<li>Nom : " . htmlspecialchars($created_user['prenom'] . ' ' . $created_user['nom']) . "</li>";
            echo "<li>Email : " . htmlspecialchars($created_user['email']) . "</li>";
            echo "<li>Username : " . htmlspecialchars($created_user['username']) . "</li>";
            echo "<li>Rôle : " . $created_user['role'] . "</li>";
            echo "<li>Statut : " . $created_user['status'] . "</li>";
            echo "<li>Créé le : " . $created_user['created_at'] . "</li>";
            echo "</ul>";
            
            // Test de connexion
            echo "<h3>3. Test de connexion avec ce compte</h3>";
            if (password_verify($test_user['password'], $created_user['password'])) {
                echo "<p style='color: green; font-weight: bold;'>✅ MOT DE PASSE VÉRIFIÉ !</p>";
                echo "<p>La connexion devrait fonctionner avec :</p>";
                echo "<ul>";
                echo "<li><strong>Username :</strong> " . htmlspecialchars($test_user['username']) . "</li>";
                echo "<li><strong>Password :</strong> " . htmlspecialchars($test_user['password']) . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ Problème de vérification du mot de passe</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Utilisateur non trouvé après création</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Échec de l'inscription</p>";
        $errorInfo = $stmt->errorInfo();
        echo "<p>Erreur SQL : " . $errorInfo[2] . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur d'inscription : " . $e->getMessage() . "</p>";
}

echo "<h2>4. Test du formulaire d'inscription</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>Formulaire de test :</h3>";
echo "<form method='POST' action='register.php' style='background: white; padding: 20px; border-radius: 5px;'>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px;'>";

echo "<div>";
echo "<label>Nom *</label><br>";
echo "<input type='text' name='nom' value='Dupont' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Prénom *</label><br>";
echo "<input type='text' name='prenom' value='Jean' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Email *</label><br>";
echo "<input type='email' name='email' value='<EMAIL>' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Téléphone</label><br>";
echo "<input type='tel' name='telephone' value='0612345678' style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Date de naissance</label><br>";
echo "<input type='date' name='date_naissance' value='1990-01-01' style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Niveau d'étude</label><br>";
echo "<select name='niveau_etude' style='width: 100%; padding: 8px;'>";
echo "<option value='bac' selected>Baccalauréat</option>";
echo "<option value='bac+2'>Bac+2</option>";
echo "</select>";
echo "</div>";

echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label>Adresse</label><br>";
echo "<textarea name='adresse' style='width: 100%; padding: 8px;'>123 Rue de la Paix, Casablanca</textarea>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label>Formation souhaitée</label><br>";
echo "<select name='formation_souhaitee' style='width: 100%; padding: 8px;'>";
echo "<option value='maintenance' selected>Maintenance industrielle</option>";
echo "<option value='automatisation'>Automatisation et Robotique</option>";
echo "<option value='electronique'>Électronique industrielle</option>";
echo "</select>";
echo "</div>";

echo "<hr>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px;'>";

echo "<div>";
echo "<label>Nom d'utilisateur *</label><br>";
echo "<input type='text' name='username' value='jean.dupont' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div>";
echo "<label>Mot de passe *</label><br>";
echo "<input type='password' name='password' value='password123' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label>Confirmer le mot de passe *</label><br>";
echo "<input type='password' name='confirm_password' value='password123' required style='width: 100%; padding: 8px;'>";
echo "</div>";

echo "<div style='margin: 20px 0;'>";
echo "<input type='submit' value='Tester l\\'inscription' style='background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
echo "</div>";

echo "</form>";
echo "</div>";

echo "<h2>5. Liens utiles</h2>";
echo "<div style='background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<ul>";
echo "<li><a href='register.php'>Page d'inscription normale</a></li>";
echo "<li><a href='login.php'>Page de connexion</a></li>";
echo "<li><a href='debug_connexion.php'>Debug connexion</a></li>";
echo "<li><a href='creer_comptes.php'>Créer les comptes de test</a></li>";
echo "<li><a href='index.php'>Retour à l'accueil</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. Diagnostic</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Si l'inscription ne fonctionne pas :</h3>";
echo "<ol>";
echo "<li>Vérifiez que tous les champs obligatoires sont remplis</li>";
echo "<li>Vérifiez que l'email et le username sont uniques</li>";
echo "<li>Vérifiez que les mots de passe correspondent</li>";
echo "<li>Vérifiez que la table users a toutes les colonnes nécessaires</li>";
echo "</ol>";
echo "</div>";
?>
