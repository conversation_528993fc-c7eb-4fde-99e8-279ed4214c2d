<?php
require_once 'config.php';
session_start();

echo "<h1>🔍 Diagnostic de connexion</h1>";

// Test 1: Vérifier la base de données
echo "<h2>1. Test de la base de données</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $total = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Base de données OK - $total utilisateurs trouvés</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur base de données : " . $e->getMessage() . "</p>";
    echo "<p><a href='test_database.php'>→ Créer la base de données</a></p>";
    exit;
}

// Test 2: Vérifier les comptes
echo "<h2>2. Vérification des comptes</h2>";
$stmt = $pdo->query("SELECT username, email, role, status FROM users ORDER BY role DESC");
$users = $stmt->fetchAll();

if (empty($users)) {
    echo "<p style='color: red;'>❌ Aucun compte trouvé</p>";
    echo "<p><a href='creer_comptes.php'>→ Créer les comptes de test</a></p>";
} else {
    echo "<p style='color: green;'>✅ Comptes trouvés :</p>";
    echo "<ul>";
    foreach ($users as $user) {
        $color = $user['status'] === 'active' ? 'green' : 'orange';
        echo "<li style='color: $color;'>" . htmlspecialchars($user['username']) . " (" . $user['role'] . ") - " . $user['status'] . "</li>";
    }
    echo "</ul>";
}

// Test 3: Simuler une connexion
echo "<h2>3. Test de connexion simulé</h2>";

if (isset($_POST['test_login'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>Résultat du test :</h3>";
    
    if (empty($username) || empty($password)) {
        echo "<p style='color: red;'>❌ Username ou mot de passe vide</p>";
    } else {
        try {
            // Rechercher l'utilisateur
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user) {
                echo "<p style='color: green;'>✅ Utilisateur trouvé : " . htmlspecialchars($user['prenom'] . ' ' . $user['nom']) . "</p>";
                echo "<p>Email : " . htmlspecialchars($user['email']) . "</p>";
                echo "<p>Username : " . htmlspecialchars($user['username']) . "</p>";
                echo "<p>Rôle : " . $user['role'] . "</p>";
                echo "<p>Statut : " . $user['status'] . "</p>";
                
                // Vérifier le mot de passe
                if (password_verify($password, $user['password'])) {
                    echo "<p style='color: green; font-weight: bold;'>✅ MOT DE PASSE CORRECT !</p>";
                    
                    // Vérifier le statut
                    if ($user['status'] === 'active') {
                        echo "<p style='color: green; font-weight: bold;'>✅ COMPTE ACTIF - La connexion devrait fonctionner !</p>";
                        
                        // Simuler la session
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['nom'] = $user['nom'];
                        $_SESSION['prenom'] = $user['prenom'];
                        $_SESSION['role'] = $user['role'];
                        
                        echo "<p style='color: blue;'>🔗 Session créée. Redirection selon le rôle :</p>";
                        if ($user['role'] === 'admin') {
                            echo "<p>→ <a href='admin-dashboard.php'>Dashboard Admin</a></p>";
                        } else {
                            echo "<p>→ <a href='index.php'>Page d'accueil</a></p>";
                        }
                        
                    } else {
                        echo "<p style='color: orange; font-weight: bold;'>⚠️ COMPTE NON ACTIF (statut: " . $user['status'] . ")</p>";
                    }
                } else {
                    echo "<p style='color: red; font-weight: bold;'>❌ MOT DE PASSE INCORRECT !</p>";
                    echo "<p>Le mot de passe fourni ne correspond pas au hash en base.</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Utilisateur non trouvé avec ce username/email</p>";
                echo "<p>Vérifiez l'orthographe ou créez un nouveau compte.</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
}

// Formulaire de test
echo "<form method='POST' style='background: #e9ecef; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>Tester une connexion :</h3>";
echo "<p>";
echo "<label>Username ou Email :</label><br>";
echo "<input type='text' name='username' value='admin' style='width: 250px; padding: 8px; margin: 5px 0;'>";
echo "</p>";
echo "<p>";
echo "<label>Mot de passe :</label><br>";
echo "<input type='password' name='password' value='password' style='width: 250px; padding: 8px; margin: 5px 0;'>";
echo "</p>";
echo "<p>";
echo "<input type='submit' name='test_login' value='Tester la connexion' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;'>";
echo "</p>";
echo "</form>";

// Test 4: Vérifier les fichiers
echo "<h2>4. Vérification des fichiers</h2>";
$files_to_check = [
    'config.php' => 'Configuration base de données',
    'login.php' => 'Page de connexion',
    'process-login.php' => 'Traitement de connexion',
    'register.php' => 'Page d\'inscription',
    'index.php' => 'Page d\'accueil'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file - $description</p>";
    } else {
        echo "<p style='color: red;'>❌ $file manquant - $description</p>";
    }
}

// Test 5: Informations de session
echo "<h2>5. Informations de session</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✅ Session active :</p>";
    echo "<ul>";
    echo "<li>User ID : " . $_SESSION['user_id'] . "</li>";
    echo "<li>Username : " . htmlspecialchars($_SESSION['username']) . "</li>";
    echo "<li>Nom : " . htmlspecialchars($_SESSION['prenom'] . ' ' . $_SESSION['nom']) . "</li>";
    echo "<li>Rôle : " . $_SESSION['role'] . "</li>";
    echo "</ul>";
    echo "<p><a href='logout.php'>Se déconnecter</a></p>";
} else {
    echo "<p style='color: orange;'>⚠️ Aucune session active</p>";
}

// Actions recommandées
echo "<hr>";
echo "<h2>🛠️ Actions recommandées</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Si les comptes n'existent pas :</h3>";
echo "<ol>";
echo "<li><a href='creer_comptes.php'>Créer les comptes de test</a></li>";
echo "<li>Ou importer le fichier base_donnees_complete.sql dans phpMyAdmin</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Si la connexion ne fonctionne pas :</h3>";
echo "<ol>";
echo "<li>Vérifier que XAMPP est démarré</li>";
echo "<li>Tester avec le formulaire ci-dessus</li>";
echo "<li>Vérifier les messages d'erreur sur <a href='login.php'>login.php</a></li>";
echo "<li>Essayer de créer un nouveau compte sur <a href='register.php'>register.php</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='index.php'>← Retour à l'accueil</a></p>";
?>
