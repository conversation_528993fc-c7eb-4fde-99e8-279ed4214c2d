<?php
require_once 'config.php';
session_start();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        header('Location: dashboard.php');
        exit();
    } else {
        $error = "Identifiants incorrects";
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Espace Stagiaire - Pôle Industrie CMC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-background">
        <div class="login-overlay"></div>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark bg-transparent position-absolute w-100" style="z-index: 1000;">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-gear-fill me-2"></i>Pôle Industrie CMC
            </a>
            <div class="d-flex">
                <a href="index.php" class="btn btn-outline-light">
                    <i class="bi bi-house me-1"></i>Accueil
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center position-relative" style="z-index: 100;">
        <div class="row justify-content-center w-100">
            <div class="col-md-8 col-lg-6 col-xl-4">
                <div class="login-card card border-0 shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <i class="bi bi-person-circle display-4 mb-3"></i>
                        <h2 class="mb-0 fw-bold">Connexion</h2>
                        <p class="mb-0 opacity-75">Espace Stagiaire</p>
                    </div>
                    <div class="card-body p-5">
                        <p class="text-center text-muted mb-4">
                            Connectez-vous pour accéder à votre tableau de bord et suivre votre progression
                        </p>

                        <?php
                        // Affichage des messages
                        if (isset($_SESSION['login_error'])) {
                            echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
                            echo '<i class="bi bi-exclamation-triangle me-2"></i>' . htmlspecialchars($_SESSION['login_error']);
                            echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                            echo '</div>';
                            unset($_SESSION['login_error']);
                        }

                        if (isset($_GET['message'])) {
                            if ($_GET['message'] === 'disconnected') {
                                echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
                                echo '<i class="bi bi-check-circle me-2"></i>Vous avez été déconnecté avec succès.';
                                echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                                echo '</div>';
                            } elseif ($_GET['message'] === 'registered') {
                                echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
                                echo '<i class="bi bi-check-circle me-2"></i>Votre compte a été créé avec succès ! Vous pouvez maintenant vous connecter.';
                                echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
                                echo '</div>';
                            }
                        }
                        ?>

                        <form action="process-login.php" method="POST" id="loginForm">
                            <div class="mb-4">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person me-2 text-primary"></i>Nom d'utilisateur
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-person text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0 ps-0" id="username" name="username"
                                           placeholder="Votre nom d'utilisateur" required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="bi bi-lock me-2 text-primary"></i>Mot de passe
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0 ps-0" id="password" name="password"
                                           placeholder="Votre mot de passe" required>
                                    <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe" name="remember">
                                    <label class="form-check-label text-muted" for="rememberMe">
                                        Se souvenir de moi
                                    </label>
                                </div>
                                <a href="#" class="text-decoration-none text-primary" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                    Mot de passe oublié?
                                </a>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 py-3 fw-semibold">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Se connecter
                            </button>
                        </form>

                        <div class="text-center mt-4 pt-4 border-top">
                            <p class="text-muted mb-3">Vous n'avez pas encore de compte?</p>
                            <a href="register.php" class="btn btn-success w-100 mb-3">
                                <i class="bi bi-person-plus me-2"></i>Créer un compte étudiant
                            </a>

                            <div class="mt-4">
                                <p class="small text-muted mb-2">
                                    <i class="bi bi-info-circle me-1 text-info"></i>
                                    Besoin d'aide ? Contactez l'administration
                                </p>
                                <div class="contact-info">
                                    <a href="tel:+212522XXXXXX" class="btn btn-outline-primary btn-sm me-2">
                                        <i class="bi bi-telephone me-1"></i>Appeler
                                    </a>
                                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-envelope me-1"></i>Email
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h3 class="h5 mb-3">Questions fréquentes</h3>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h4 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Comment obtenir mes identifiants de connexion?
                                </button>
                            </h4>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Contactez l'administration du centre pour obtenir vos identifiants.
                                </div>
                            </div>
                        </div>
                        <!-- Autres questions FAQ -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>Pôle Industrie CMC</h5>
                    <p>Centre d'excellence pour la formation industrielle et technique</p>
                </div>
                <div class="col-md-4">
                    <h5>Liens Rapides</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">Accueil</a></li>
                        <li><a href="#" class="text-light">Formations</a></li>
                        <li><a href="#" class="text-light">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact</h5>
                    <address>
                        123 Avenue de l'Industrie<br>
                        Casablanca, Maroc<br>
                        Email: <EMAIL><br>
                        Tél: +212 500 XXXXXX
                    </address>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>