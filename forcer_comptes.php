<?php
require_once 'config.php';

echo "<h1>🔧 Forcer la création des comptes</h1>";
echo "<p>Ce script va supprimer tous les comptes existants et en créer de nouveaux avec des mots de passe garantis.</p>";

try {
    // Supprimer tous les comptes existants
    $pdo->exec("DELETE FROM users");
    echo "<p style='color: orange;'>🗑️ Tous les comptes existants supprimés</p>";
    
    // Créer les nouveaux comptes avec des mots de passe hashés correctement
    $comptes = [
        // ADMIN
        [
            'nom' => 'Admin',
            'prenom' => 'Système',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'password' => 'password',
            'role' => 'admin',
            'status' => 'active'
        ],
        
        // ÉTUDIANTS
        [
            'nom' => 'Alaoui',
            'prenom' => 'Mohammed',
            'email' => '<EMAIL>',
            'username' => 'mohammed.alaoui',
            'password' => 'password',
            'role' => 'student',
            'status' => 'active'
        ],
        
        [
            'nom' => 'Benali',
            'prenom' => 'Fatima',
            'email' => '<EMAIL>',
            'username' => 'fatima.benali',
            'password' => 'password',
            'role' => 'student',
            'status' => 'active'
        ],
        
        [
            'nom' => 'Tazi',
            'prenom' => 'Youssef',
            'email' => '<EMAIL>',
            'username' => 'youssef.tazi',
            'password' => 'password',
            'role' => 'student',
            'status' => 'active'
        ],
        
        [
            'nom' => 'Test',
            'prenom' => 'Utilisateur',
            'email' => '<EMAIL>',
            'username' => 'test',
            'password' => 'password',
            'role' => 'student',
            'status' => 'active'
        ]
    ];
    
    echo "<h2>Création des comptes :</h2>";
    
    foreach ($comptes as $compte) {
        // Hasher le mot de passe
        $hashed_password = password_hash($compte['password'], PASSWORD_DEFAULT);
        
        // Insérer le compte
        $stmt = $pdo->prepare("
            INSERT INTO users (nom, prenom, email, username, password, role, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $compte['nom'],
            $compte['prenom'],
            $compte['email'],
            $compte['username'],
            $hashed_password,
            $compte['role'],
            $compte['status']
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ " . htmlspecialchars($compte['prenom'] . ' ' . $compte['nom']) . " (" . $compte['username'] . ")</p>";
            
            // Vérifier immédiatement que le mot de passe fonctionne
            if (password_verify($compte['password'], $hashed_password)) {
                echo "<p style='color: green; margin-left: 20px;'>✅ Mot de passe vérifié</p>";
            } else {
                echo "<p style='color: red; margin-left: 20px;'>❌ Problème de mot de passe</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Erreur pour " . htmlspecialchars($compte['prenom'] . ' ' . $compte['nom']) . "</p>";
        }
    }
    
    // Vérifier le total créé
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $total = $stmt->fetch()['total'];
    
    echo "<hr>";
    echo "<h2 style='color: green;'>🎉 Création terminée !</h2>";
    echo "<p><strong>Total des comptes créés : $total</strong></p>";
    
    // Test de connexion automatique pour chaque compte
    echo "<h2>🧪 Test de connexion pour chaque compte :</h2>";
    
    $stmt = $pdo->query("SELECT username, password FROM users");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        if (password_verify('password', $user['password'])) {
            echo "<p style='color: green;'>✅ " . htmlspecialchars($user['username']) . " → Mot de passe OK</p>";
        } else {
            echo "<p style='color: red;'>❌ " . htmlspecialchars($user['username']) . " → Problème de mot de passe</p>";
        }
    }
    
    // Afficher les informations de connexion
    echo "<div style='background: #d4edda; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>🔑 Informations de connexion :</h2>";
    echo "<p><strong>Tous les mots de passe sont :</strong> <code>password</code></p>";
    echo "<h3>Comptes disponibles :</h3>";
    echo "<ul>";
    echo "<li><strong>Admin :</strong> username = <code>admin</code>, password = <code>password</code></li>";
    echo "<li><strong>Étudiant 1 :</strong> username = <code>mohammed.alaoui</code>, password = <code>password</code></li>";
    echo "<li><strong>Étudiant 2 :</strong> username = <code>fatima.benali</code>, password = <code>password</code></li>";
    echo "<li><strong>Étudiant 3 :</strong> username = <code>youssef.tazi</code>, password = <code>password</code></li>";
    echo "<li><strong>Test :</strong> username = <code>test</code>, password = <code>password</code></li>";
    echo "</ul>";
    echo "</div>";
    
    // Test de connexion en direct
    echo "<div style='background: #cce5ff; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>🚀 Test immédiat :</h2>";
    echo "<p>Testez maintenant la connexion :</p>";
    echo "<ol>";
    echo "<li><a href='login.php' target='_blank'>Ouvrir la page de connexion</a></li>";
    echo "<li>Utiliser : username = <code>admin</code>, password = <code>password</code></li>";
    echo "<li>Ou utiliser : username = <code>test</code>, password = <code>password</code></li>";
    echo "</ol>";
    echo "</div>";
    
    // Diagnostic automatique
    echo "<div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>🔍 Diagnostic automatique :</h2>";
    echo "<p><a href='diagnostic_complet.php'>Lancer le diagnostic complet</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>XAMPP est démarré</li>";
    echo "<li>MySQL fonctionne</li>";
    echo "<li>La base de données existe</li>";
    echo "<li>La table users existe</li>";
    echo "</ul>";
    echo "<p><a href='test_database.php'>→ Créer la base de données</a></p>";
}

echo "<p><a href='index.php'>← Retour à l'accueil</a></p>";
?>
