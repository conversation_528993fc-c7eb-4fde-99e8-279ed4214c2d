-- Base de données complète pour Pôle Industrie CMC
-- Exécutez ce script dans phpMyAdmin pour créer la base avec des comptes de test

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie_cmc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pole_industrie_cmc;

-- Suppression des tables existantes (si elles existent)
DROP TABLE IF EXISTS users;

-- Création de la table users
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse TEXT,
    formation_souhaitee VARCHAR(100),
    niveau_etude VARCHAR(50),
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'admin', 'teacher') DEFAULT 'student',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    avatar VARCHAR(255),
    biographie TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertion des comptes de test
-- IMPORTANT: Tous les mots de passe sont "password"

-- 1. COMPTE ADMINISTRATEUR
INSERT INTO users (nom, prenom, email, telephone, username, password, role, status, biographie, created_at) VALUES 
('Admin', 'Système', '<EMAIL>', '0522123456', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', 'Compte administrateur du système', '2024-01-01 08:00:00');

-- 2. COMPTES ÉTUDIANTS DE TEST

-- Étudiant 1 - Maintenance industrielle
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Alaoui', 'Mohammed', '<EMAIL>', '0612345678', '1998-06-15', '123 Avenue Mohammed V, Casablanca', 'maintenance', 'bac', 'mohammed.alaoui', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Passionné par la maintenance industrielle et les nouvelles technologies.', '2024-02-15 09:30:00');

-- Étudiant 2 - Automatisation
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Benali', 'Fatima', '<EMAIL>', '0623456789', '1999-03-22', '456 Rue Hassan II, Rabat', 'automatisation', 'bac+2', 'fatima.benali', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Intéressée par la robotique et l\'automatisation industrielle.', '2024-02-20 10:15:00');

-- Étudiant 3 - Électronique
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Tazi', 'Youssef', '<EMAIL>', '0634567890', '1997-11-08', '789 Boulevard Zerktouni, Casablanca', 'electronique', 'bac', 'youssef.tazi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Spécialisé en électronique de puissance et systèmes embarqués.', '2024-02-25 14:20:00');

-- Étudiant 4 - Mécanique
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Idrissi', 'Aicha', '<EMAIL>', '0645678901', '1998-09-12', '321 Avenue des FAR, Fès', 'mecanique', 'bac', 'aicha.idrissi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Passionnée par la mécanique de précision et l\'usinage.', '2024-03-01 11:45:00');

-- Étudiant 5 - Soudure
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Benjelloun', 'Omar', '<EMAIL>', '0656789012', '1996-12-03', '654 Rue Allal Ben Abdellah, Salé', 'soudure', 'bac', 'omar.benjelloun', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Expert en soudage industriel et métallurgie.', '2024-03-05 16:30:00');

-- Étudiant 6 - Contrôle qualité
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Fassi', 'Laila', '<EMAIL>', '0667890123', '1999-07-18', '987 Avenue Moulay Youssef, Marrakech', 'qualite', 'bac+2', 'laila.fassi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'active', 'Spécialisée en contrôle qualité et normes ISO.', '2024-03-10 13:15:00');

-- Étudiant 7 - En attente d'activation
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Chakir', 'Hamid', '<EMAIL>', '0678901234', '1998-04-25', '147 Rue Ibn Sina, Agadir', 'maintenance', 'bac', 'hamid.chakir', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'pending', 'Nouveau candidat en attente d\'activation.', '2024-03-15 09:00:00');

-- Étudiant 8 - Compte inactif
INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie, created_at) VALUES 
('Amrani', 'Nadia', '<EMAIL>', '0689012345', '1997-08-14', '258 Boulevard Mohammed VI, Tanger', 'electronique', 'bac+3', 'nadia.amrani', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'inactive', 'Compte temporairement désactivé.', '2024-01-20 15:45:00');

-- 3. COMPTE FORMATEUR (TEACHER)
INSERT INTO users (nom, prenom, email, telephone, username, password, role, status, biographie, created_at) VALUES 
('Bennani', 'Ahmed', '<EMAIL>', '0522987654', 'prof.bennani', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'teacher', 'active', 'Formateur expert en maintenance industrielle avec 15 ans d\'expérience.', '2024-01-10 08:30:00');

-- Affichage des comptes créés
SELECT 'COMPTES CRÉÉS AVEC SUCCÈS' as Message;

SELECT 
    id,
    CONCAT(prenom, ' ', nom) as 'Nom Complet',
    username as 'Nom d\'utilisateur',
    email as 'Email',
    role as 'Rôle',
    status as 'Statut',
    formation_souhaitee as 'Formation',
    created_at as 'Créé le'
FROM users 
ORDER BY role DESC, created_at ASC;

-- Instructions d'utilisation
SELECT '=== INSTRUCTIONS D\'UTILISATION ===' as '';
SELECT 'Tous les mots de passe sont : password' as 'Mot de passe';
SELECT '' as '';
SELECT 'COMPTES ADMINISTRATEUR :' as '';
SELECT 'Username: admin | Email: <EMAIL>' as 'Admin';
SELECT '' as '';
SELECT 'COMPTES ÉTUDIANTS ACTIFS :' as '';
SELECT 'Username: mohammed.alaoui | Email: <EMAIL>' as 'Étudiant 1';
SELECT 'Username: fatima.benali | Email: <EMAIL>' as 'Étudiant 2';
SELECT 'Username: youssef.tazi | Email: <EMAIL>' as 'Étudiant 3';
SELECT 'Username: aicha.idrissi | Email: <EMAIL>' as 'Étudiant 4';
SELECT 'Username: omar.benjelloun | Email: <EMAIL>' as 'Étudiant 5';
SELECT 'Username: laila.fassi | Email: <EMAIL>' as 'Étudiant 6';
SELECT '' as '';
SELECT 'COMPTE FORMATEUR :' as '';
SELECT 'Username: prof.bennani | Email: <EMAIL>' as 'Formateur';
SELECT '' as '';
SELECT 'COMPTES DE TEST (statuts spéciaux) :' as '';
SELECT 'Username: hamid.chakir (statut: pending)' as 'En attente';
SELECT 'Username: nadia.amrani (statut: inactive)' as 'Inactif';
SELECT '' as '';
SELECT 'Pour tester : http://localhost/soutnance/login.php' as 'Page de connexion';
