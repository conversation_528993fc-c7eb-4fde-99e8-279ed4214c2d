<?php
require_once 'config.php';
session_start();
// Vérification de la connexion
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Données du profil (à remplacer par les données de la base de données)
$profile = [
    'nom' => '<PERSON><PERSON><PERSON>',
    'formation' => 'Maintenance industrielle - 2ème année',
    'groupe' => 'Groupe A1',
    'email' => '<EMAIL>',
    'telephone' => '0612345678',
    'date_naissance' => '15/06/1998',
    'adresse' => '123 Avenue de l\'Industrie, Casablanca',
    'biographie' => 'Passionné par la maintenance industrielle et les nouvelles technologies, je cherche à approfondir mes connaissances et compétences dans ce domaine.'
];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Espace Stagiaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <!-- Même navigation que dashboard.php -->
    </nav>

    <div class="container my-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group mb-4">
                    <a href="#" class="list-group-item list-group-item-action active">Mon profil</a>
                    <a href="#" class="list-group-item list-group-item-action">Mes formations</a>
                    <a href="#" class="list-group-item list-group-item-action">Emploi du temps</a>
                    <a href="#" class="list-group-item list-group-item-action">Documents</a>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2 class="card-title">Mon profil</h2>
                            <button class="btn btn-primary">Modifier le profil</button>
                        </div>

                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="profile-image mb-3">
                                    <img src="images/default-avatar.png" alt="Photo de profil" class="rounded-circle img-fluid">
                                </div>
                            </div>
                            <div class="col-md-9">
                                <h3 class="h4"><?php echo htmlspecialchars($profile['nom']); ?></h3>
                                <p class="text-muted">
                                    <?php echo htmlspecialchars($profile['formation']); ?><br>
                                    <?php echo htmlspecialchars($profile['groupe']); ?>
                                </p>

                                <hr>

                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Email:</strong><br>
                                        <?php echo htmlspecialchars($profile['email']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Téléphone:</strong><br>
                                        <?php echo htmlspecialchars($profile['telephone']); ?></p>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Date de naissance:</strong><br>
                                        <?php echo htmlspecialchars($profile['date_naissance']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Adresse:</strong><br>
                                        <?php echo htmlspecialchars($profile['adresse']); ?></p>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <h4 class="h5">Biographie</h4>
                                    <p><?php echo htmlspecialchars($profile['biographie']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <!-- Même footer que précédemment -->
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>