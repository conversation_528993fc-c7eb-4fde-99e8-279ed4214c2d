# 🔑 Comptes de Test - Pôle Industrie CMC

## 📋 Instructions d'installation

1. **Ouvrir phpMyAdmin** : `http://localhost/phpmyadmin/`
2. **Importer le fichier** : `base_donnees_complete.sql`
3. **Tester la connexion** : `http://localhost/soutnance/login.php`

---

## 👨‍💼 COMPTE ADMINISTRATEUR

| Champ | Valeur |
|-------|--------|
| **Username** | `admin` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Rôle** | Administrateur |
| **Accès** | Dashboard d'administration |

---

## 🎓 COMPTES ÉTUDIANTS ACTIFS

### Étudiant 1 - Maintenance Industrielle
| Champ | Valeur |
|-------|--------|
| **Nom** | <PERSON> |
| **Username** | `mohammed.alaoui` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Maintenance industrielle |
| **Téléphone** | 0612345678 |

### Étudiant 2 - Automatisation
| Champ | Valeur |
|-------|--------|
| **Nom** | Fatima Benali |
| **Username** | `fatima.benali` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Automatisation et Robotique |
| **Téléphone** | 0623456789 |

### Étudiant 3 - Électronique
| Champ | Valeur |
|-------|--------|
| **Nom** | Youssef Tazi |
| **Username** | `youssef.tazi` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Électronique industrielle |
| **Téléphone** | 0634567890 |

### Étudiant 4 - Mécanique
| Champ | Valeur |
|-------|--------|
| **Nom** | Aicha Idrissi |
| **Username** | `aicha.idrissi` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Mécanique industrielle |
| **Téléphone** | 0645678901 |

### Étudiant 5 - Soudure
| Champ | Valeur |
|-------|--------|
| **Nom** | Omar Benjelloun |
| **Username** | `omar.benjelloun` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Soudure industrielle |
| **Téléphone** | 0656789012 |

### Étudiant 6 - Contrôle Qualité
| Champ | Valeur |
|-------|--------|
| **Nom** | Laila Fassi |
| **Username** | `laila.fassi` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Formation** | Contrôle qualité |
| **Téléphone** | 0667890123 |

---

## 👨‍🏫 COMPTE FORMATEUR

| Champ | Valeur |
|-------|--------|
| **Nom** | Ahmed Bennani |
| **Username** | `prof.bennani` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Rôle** | Formateur |
| **Spécialité** | Maintenance industrielle |

---

## ⚠️ COMPTES DE TEST (Statuts spéciaux)

### Compte en attente d'activation
| Champ | Valeur |
|-------|--------|
| **Nom** | Hamid Chakir |
| **Username** | `hamid.chakir` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Statut** | `pending` (en attente) |

### Compte inactif
| Champ | Valeur |
|-------|--------|
| **Nom** | Nadia Amrani |
| **Username** | `nadia.amrani` |
| **Email** | `<EMAIL>` |
| **Mot de passe** | `password` |
| **Statut** | `inactive` (désactivé) |

---

## 🚀 Test Rapide

### Connexion Admin
1. Aller sur : `http://localhost/soutnance/login.php`
2. Username : `admin`
3. Password : `password`
4. → Redirection vers le dashboard admin

### Connexion Étudiant
1. Aller sur : `http://localhost/soutnance/login.php`
2. Username : `mohammed.alaoui` (ou n'importe quel autre étudiant)
3. Password : `password`
4. → Redirection vers l'accueil

---

## 📊 Statistiques de la base

- **Total utilisateurs** : 9
- **Administrateurs** : 1
- **Étudiants actifs** : 6
- **Formateurs** : 1
- **Comptes en attente** : 1
- **Comptes inactifs** : 1

---

## 🔧 Gestion des comptes (Admin)

L'administrateur peut :
- ✅ Voir tous les utilisateurs
- ✅ Activer/désactiver des comptes
- ✅ Supprimer des utilisateurs
- ✅ Changer les rôles
- ✅ Rechercher et filtrer

---

## 📝 Notes importantes

- 🔒 **Tous les mots de passe sont hashés** avec `password_hash()`
- 🔑 **Mot de passe universel** : `password` (pour les tests)
- 📧 **Emails uniques** : Chaque utilisateur a un email unique
- 👤 **Usernames uniques** : Chaque utilisateur a un username unique
- 🎯 **Données réalistes** : Noms, adresses et formations cohérents

---

## 🆘 Dépannage

Si la connexion ne fonctionne pas :
1. Vérifier que XAMPP est démarré
2. Vérifier que la base `pole_industrie_cmc` existe
3. Tester avec : `http://localhost/soutnance/test_database.php`
4. Vérifier les messages d'erreur
