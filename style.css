/* Styles généraux */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    padding-top: 76px; /* Pour compenser la navbar fixe */
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    transform: translateY(-2px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: #ffc107;
    border-radius: 2px;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 50%, #0dcaf0 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.animate-fade-in {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.hero-stats .stat-item:hover {
    transform: translateY(-5px);
}

/* Floating elements */
.floating-elements {
    position: relative;
    height: 400px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 50%;
    right: 20%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.card-body {
    padding: 2rem;
}

/* Stats */
.stat-box {
    padding: 2.5rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d6efd, #6610f2, #0dcaf0);
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stat-box h2 {
    color: #0d6efd;
    font-weight: bold;
    font-size: 3rem;
}

/* Badges */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 25px;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    padding: 0.75rem 2rem;
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    border: none;
    color: #000;
}

.btn-outline-primary:hover,
.btn-outline-light:hover {
    transform: translateY(-2px);
}

/* Sections */
section {
    padding: 5rem 0;
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Footer */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, #212529 0%, #343a40 100%) !important;
}

footer a {
    text-decoration: none;
    transition: all 0.3s ease;
}

footer a:hover {
    color: #ffc107 !important;
    transform: translateX(5px);
}

.social-links a {
    display: inline-block;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px) scale(1.1);
    color: #ffc107 !important;
}

.contact-info p {
    transition: all 0.3s ease;
}

.contact-info p:hover {
    transform: translateX(5px);
    color: #ffc107 !important;
}

/* Animations supplémentaires */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Effets de survol pour les icônes */
.bi {
    transition: all 0.3s ease;
}

.card:hover .bi {
    transform: scale(1.1);
    color: #ffc107 !important;
}

/* Responsive */
@media (max-width: 768px) {
    body {
        padding-top: 60px;
    }

    .hero-section {
        padding: 3rem 0;
        min-height: 80vh;
    }

    .display-3 {
        font-size: 2.5rem;
    }

    .hero-stats {
        margin: 2rem 0;
    }

    .floating-elements {
        display: none;
    }

    .stat-box {
        margin-bottom: 2rem;
        padding: 2rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    section {
        padding: 3rem 0;
    }

    .card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .display-3 {
        font-size: 2rem;
    }

    .btn-lg {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    .hero-stats .col-4 {
        margin-bottom: 1rem;
    }

    .stat-box h2 {
        font-size: 2rem;
    }
}

/* Effets de chargement */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Scroll smooth */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #6610f2, #0dcaf0);
}

/* Page de connexion */
.login-page {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 50%, #0dcaf0 100%);
    z-index: 1;
}

.login-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.login-card {
    backdrop-filter: blur(10px);
    border-radius: 20px !important;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-card .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
    border: none;
}

.login-card .form-control {
    border-radius: 10px;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.login-card .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.login-card .input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid #e9ecef;
    border-right: none;
}

.login-card .btn-primary {
    border-radius: 10px;
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    transition: all 0.3s ease;
}

.login-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

.contact-info .btn {
    border-radius: 20px;
    transition: all 0.3s ease;
}

.contact-info .btn:hover {
    transform: translateY(-2px);
}

/* Modal styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* Responsive pour la page de connexion */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
    }

    .login-card .card-body {
        padding: 2rem !important;
    }

    .contact-info .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}