# Guide d'Installation - Pôle Industrie CMC

## 🚀 Installation Rapide

### Étape 1: Démarrer XAMPP
1. Ouvrir XAMPP Control Panel
2. Démarrer **Apache** et **MySQL**
3. Vérifier que les deux services sont en vert

### Étape 2: Tester la base de données
1. Aller sur: `http://localhost/soutnance/test_database.php`
2. Cette page va automatiquement :
   - Créer la base de données si elle n'existe pas
   - Créer la table `users` 
   - Créer un compte admin par défaut

### Étape 3: Tester le système
1. **Page d'accueil**: `http://localhost/soutnance/`
2. **Inscription**: `http://localhost/soutnance/register.php`
3. **Connexion**: `http://localhost/soutnance/login.php`

## 🔑 Comptes par défaut

### Administrateur
- **Username**: `admin`
- **Password**: `password`
- **Accès**: Dashboard d'administration

## 📋 Fonctionnalités

### ✅ Navigation simplifiée
- **Accueil** → Page principale
- **Formations** → Liste des formations
- **Actualités** → Actualités du centre
- **Connexion** → Page de connexion/inscription

### ✅ Système de connexion
- **Étudiants** → Redirigés vers l'accueil après connexion
- **Administrateurs** → Redirigés vers le dashboard admin

### ✅ Inscription
- Formulaire complet d'inscription
- Validation des données
- Création automatique du compte

## 🛠️ Dépannage

### Problème de base de données
1. Aller sur `http://localhost/soutnance/test_database.php`
2. Suivre les instructions affichées
3. Vérifier que XAMPP est bien démarré

### Problème d'inscription
1. Vérifier que la table `users` existe
2. Tester avec des données simples
3. Vérifier les messages d'erreur

### Page blanche
1. Vérifier que Apache est démarré
2. Vérifier l'URL : `http://localhost/soutnance/`
3. Regarder les erreurs dans XAMPP

## 📁 Structure des fichiers

```
soutnance/
├── index.php              # Page d'accueil
├── login.php              # Connexion
├── register.php           # Inscription
├── logout.php             # Déconnexion
├── formations.php         # Liste des formations
├── formation-detail.php   # Détail d'une formation
├── actualites.php         # Actualités
├── admin-dashboard.php    # Dashboard admin
├── admin-users.php        # Gestion des utilisateurs
├── config.php             # Configuration BDD
├── process-login.php      # Traitement connexion
├── test_database.php      # Test de la BDD
└── style.css              # Styles CSS
```

## 🎯 Test complet

1. **Tester l'accueil** : `http://localhost/soutnance/`
2. **Créer un compte** : Cliquer sur "Connexion" → "Créer un compte"
3. **Se connecter** : Utiliser le compte créé ou le compte admin
4. **Navigation** : Tester toutes les pages du menu

## ⚡ Raccourcis

- **Accueil** : `http://localhost/soutnance/`
- **Test BDD** : `http://localhost/soutnance/test_database.php`
- **Admin** : `http://localhost/soutnance/admin-dashboard.php`
- **phpMyAdmin** : `http://localhost/phpmyadmin/`

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez que XAMPP est démarré
2. Testez la base de données avec `test_database.php`
3. Vérifiez les messages d'erreur affichés
4. Consultez les logs d'erreur dans XAMPP
