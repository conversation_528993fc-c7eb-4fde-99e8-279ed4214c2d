# Installation du Système de Gestion des Comptes - Pôle Industrie CMC

## Prérequis

- XAMPP (Apache + MySQL + PHP)
- Navigateur web moderne
- Éditeur de texte (optionnel)

## Étapes d'installation

### 1. Configuration de la base de données

1. **Démarrer XAMPP**
   - Ouvrir XAMPP Control Panel
   - Démarrer Apache et MySQL

2. **Créer la base de données**
   - Ouvrir phpMyAdmin (http://localhost/phpmyadmin)
   - Cliquer sur "Nouveau" pour créer une nouvelle base de données
   - Nommer la base de données : `pole_industrie_cmc`
   - Sélectionner l'encodage : `utf8mb4_unicode_ci`

3. **Importer la structure**
   - Sélectionner la base de données créée
   - Aller dans l'onglet "Importer"
   - Choisir le fichier `database_setup.sql`
   - Cliquer sur "Exécuter"

### 2. Configuration du projet

1. **Vérifier le fichier config.php**
   ```php
   <?php
   $host = 'localhost';
   $dbname = 'pole_industrie_cmc';
   $username = 'root';
   $password = '';
   ```

2. **Tester la connexion**
   - Ouvrir http://localhost/soutnance/
   - Vérifier que la page d'accueil s'affiche correctement

### 3. Comptes par défaut

Le script d'installation crée automatiquement :

#### Compte Administrateur
- **Username:** admin
- **Email:** <EMAIL>
- **Mot de passe:** password
- **Rôle:** Administrateur

#### Compte Étudiant de test
- **Username:** mohammed.alaoui
- **Email:** <EMAIL>
- **Mot de passe:** password
- **Rôle:** Étudiant

### 4. Fonctionnalités disponibles

#### Pour les visiteurs :
- ✅ Consultation des pages publiques (accueil, formations, actualités)
- ✅ Inscription en ligne
- ✅ Connexion

#### Pour les étudiants :
- ✅ Dashboard personnel
- ✅ Gestion du profil
- ✅ Consultation de l'emploi du temps
- ✅ Suivi de progression
- ✅ Accès aux actualités

#### Pour les administrateurs :
- ✅ Dashboard d'administration
- ✅ Gestion des utilisateurs (activation, désactivation, suppression)
- ✅ Statistiques du système
- ✅ Gestion des rôles

### 5. Structure des pages

```
soutnance/
├── index.php              # Page d'accueil
├── login.php              # Connexion
├── register.php           # Inscription
├── logout.php             # Déconnexion
├── dashboard.php          # Dashboard étudiant
├── profile.php            # Profil utilisateur
├── formations.php         # Liste des formations
├── formation-detail.php   # Détail d'une formation
├── actualites.php         # Actualités
├── admin-dashboard.php    # Dashboard admin
├── admin-users.php        # Gestion des utilisateurs
├── config.php             # Configuration BDD
├── process-login.php      # Traitement connexion
├── database_setup.sql     # Structure BDD
└── style.css              # Styles CSS
```

### 6. Test du système

1. **Test de l'inscription**
   - Aller sur http://localhost/soutnance/register.php
   - Créer un nouveau compte
   - Vérifier la redirection vers la page de connexion

2. **Test de la connexion étudiant**
   - Se connecter avec le compte de test ou un compte créé
   - Vérifier l'accès au dashboard étudiant

3. **Test de la connexion admin**
   - Se connecter avec le compte admin
   - Vérifier l'accès au dashboard d'administration
   - Tester la gestion des utilisateurs

### 7. Sécurité

- ✅ Mots de passe hashés avec password_hash()
- ✅ Protection contre les injections SQL (requêtes préparées)
- ✅ Validation des données côté serveur
- ✅ Gestion des sessions sécurisée
- ✅ Vérification des rôles et permissions

### 8. Personnalisation

#### Modifier les informations de contact :
Éditer les fichiers suivants et remplacer les informations de contact :
- `index.php`
- `formations.php`
- `actualites.php`
- `dashboard.php`
- `profile.php`
- `formation-detail.php`

#### Ajouter de nouvelles formations :
1. Aller dans le dashboard admin
2. Utiliser la section "Gestion des formations" (à développer)
3. Ou modifier directement la base de données

#### Modifier les couleurs et styles :
Éditer le fichier `style.css` pour personnaliser :
- Couleurs principales
- Polices
- Espacements
- Animations

### 9. Dépannage

#### Erreur de connexion à la base de données :
- Vérifier que MySQL est démarré dans XAMPP
- Vérifier les paramètres dans `config.php`
- Vérifier que la base de données existe

#### Page blanche :
- Activer l'affichage des erreurs PHP
- Vérifier les logs d'erreur Apache
- Vérifier les permissions des fichiers

#### Problème de session :
- Vérifier que les cookies sont activés
- Vider le cache du navigateur
- Redémarrer Apache

### 10. Support

Pour toute question ou problème :
- Vérifier les logs d'erreur dans XAMPP
- Consulter la documentation PHP/MySQL
- Vérifier la console développeur du navigateur

## Fonctionnalités futures possibles

- 📧 Système d'email pour les notifications
- 📁 Gestion des documents et fichiers
- 📊 Rapports et statistiques avancées
- 🔔 Système de notifications en temps réel
- 📱 Version mobile responsive améliorée
- 🌐 API REST pour intégrations externes
