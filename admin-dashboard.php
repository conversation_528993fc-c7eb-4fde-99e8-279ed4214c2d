<?php
require_once 'config.php';
session_start();

// Vérification de la connexion et du rôle admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Récupération des statistiques
try {
    // Nombre total d'utilisateurs
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'student'");
    $total_students = $stmt->fetch()['total'];

    // Nombre d'inscriptions en attente
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM inscriptions WHERE status = 'pending'");
    $pending_inscriptions = $stmt->fetch()['total'];

    // Nombre de formations actives
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM formations WHERE status = 'active'");
    $active_formations = $stmt->fetch()['total'];

    // Utilisateurs récents
    $stmt = $pdo->query("SELECT * FROM users WHERE role = 'student' ORDER BY created_at DESC LIMIT 10");
    $recent_users = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Erreur de base de données : " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - Pôle Industrie CMC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="admin-dashboard.php">
                <i class="bi bi-shield-check me-2"></i>Administration CMC
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="admin-dashboard.php">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin-users.php">
                            <i class="bi bi-people me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin-formations.php">
                            <i class="bi bi-book me-1"></i>Formations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin-actualites.php">
                            <i class="bi bi-newspaper me-1"></i>Actualités
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <span class="navbar-text me-3">
                        <i class="bi bi-person-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['prenom'] . ' ' . $_SESSION['nom']); ?>
                    </span>
                    <a href="index.php" class="btn btn-outline-light me-2">
                        <i class="bi bi-house me-1"></i>Site
                    </a>
                    <a href="logout.php" class="btn btn-light">
                        <i class="bi bi-box-arrow-right me-1"></i>Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section Admin -->
    <section class="bg-danger text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="bi bi-shield-check me-3"></i>Administration
                    </h1>
                    <p class="lead fs-5">
                        Panneau d'administration du Pôle Industrie CMC. 
                        Gérez les utilisateurs, formations et contenus.
                    </p>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="hero-stats">
                        <div class="stat-item bg-white bg-opacity-10 rounded-3 p-3">
                            <i class="bi bi-gear-fill display-4 text-warning mb-2"></i>
                            <h5 class="text-white">Système de gestion</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-5">
        <!-- Statistiques principales -->
        <div class="row mb-5">
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm bg-primary text-white">
                    <div class="card-body">
                        <i class="bi bi-people display-4 mb-2"></i>
                        <h4 class="fw-bold"><?php echo $total_students; ?></h4>
                        <p class="mb-0">Étudiants inscrits</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm bg-warning text-dark">
                    <div class="card-body">
                        <i class="bi bi-clock-history display-4 mb-2"></i>
                        <h4 class="fw-bold"><?php echo $pending_inscriptions; ?></h4>
                        <p class="mb-0">Inscriptions en attente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm bg-success text-white">
                    <div class="card-body">
                        <i class="bi bi-book display-4 mb-2"></i>
                        <h4 class="fw-bold"><?php echo $active_formations; ?></h4>
                        <p class="mb-0">Formations actives</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm bg-info text-white">
                    <div class="card-body">
                        <i class="bi bi-graph-up display-4 mb-2"></i>
                        <h4 class="fw-bold">95%</h4>
                        <p class="mb-0">Taux de réussite</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Actions rapides -->
            <div class="col-md-6">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>Actions rapides
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="admin-users.php?action=new" class="btn btn-outline-primary">
                                <i class="bi bi-person-plus me-2"></i>Ajouter un utilisateur
                            </a>
                            <a href="admin-formations.php?action=new" class="btn btn-outline-success">
                                <i class="bi bi-plus-circle me-2"></i>Créer une formation
                            </a>
                            <a href="admin-actualites.php?action=new" class="btn btn-outline-info">
                                <i class="bi bi-newspaper me-2"></i>Publier une actualité
                            </a>
                            <a href="admin-users.php?filter=pending" class="btn btn-outline-warning">
                                <i class="bi bi-clock me-2"></i>Gérer les inscriptions
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Utilisateurs récents -->
            <div class="col-md-6">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-person-check me-2"></i>Nouveaux utilisateurs
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_users)): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($recent_users, 0, 5) as $user): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['prenom'] . ' ' . $user['nom']); ?></strong><br>
                                            <small class="text-muted">
                                                <i class="bi bi-envelope me-1"></i><?php echo htmlspecialchars($user['email']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                <?php echo date('d/m/Y', strtotime($user['created_at'])); ?>
                                            </small><br>
                                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="admin-users.php" class="btn btn-sm btn-outline-primary">
                                    Voir tous les utilisateurs
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">Aucun nouvel utilisateur</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques et statistiques avancées -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Aperçu du système
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="border-end">
                                    <h6 class="text-muted">Aujourd'hui</h6>
                                    <h4 class="text-primary">5</h4>
                                    <small>Nouvelles inscriptions</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="border-end">
                                    <h6 class="text-muted">Cette semaine</h6>
                                    <h4 class="text-success">23</h4>
                                    <small>Connexions</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="border-end">
                                    <h6 class="text-muted">Ce mois</h6>
                                    <h4 class="text-warning">156</h4>
                                    <small>Activités</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="border-end">
                                    <h6 class="text-muted">Formations</h6>
                                    <h4 class="text-info">12</h4>
                                    <small>Disponibles</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="border-end">
                                    <h6 class="text-muted">Actualités</h6>
                                    <h4 class="text-danger">8</h4>
                                    <small>Publiées</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div>
                                    <h6 class="text-muted">Système</h6>
                                    <h4 class="text-success">
                                        <i class="bi bi-check-circle"></i>
                                    </h4>
                                    <small>Opérationnel</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-shield-check me-2 text-warning"></i>Administration CMC
                    </h5>
                    <p class="mb-3">
                        Panneau d'administration du Pôle Industrie CMC pour la gestion 
                        des utilisateurs, formations et contenus.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h6 class="fw-bold mb-3">Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="admin-users.php" class="text-light-emphasis text-decoration-none">Gestion des utilisateurs</a></li>
                        <li><a href="admin-formations.php" class="text-light-emphasis text-decoration-none">Gestion des formations</a></li>
                        <li><a href="admin-actualites.php" class="text-light-emphasis text-decoration-none">Gestion des actualités</a></li>
                        <li><a href="index.php" class="text-light-emphasis text-decoration-none">Retour au site</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 Pôle Industrie CMC - Administration. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
