<?php
require_once 'config.php';
session_start();

$response = ['success' => false, 'message' => '', 'redirect' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);

    if (empty($username) || empty($password)) {
        $response['message'] = "Veuillez remplir tous les champs.";
    } else {
        try {
            // Rechercher l'utilisateur par nom d'utilisateur ou email
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Vérifier le statut du compte
                if ($user['status'] === 'inactive') {
                    $response['message'] = "Votre compte est désactivé. Contactez l'administration.";
                } elseif ($user['status'] === 'pending') {
                    $response['message'] = "Votre compte est en attente d'activation. Contactez l'administration.";
                } else {
                    // Connexion réussie
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['nom'] = $user['nom'];
                    $_SESSION['prenom'] = $user['prenom'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['last_activity'] = time();

                    // Mettre à jour la dernière connexion
                    $stmt = $pdo->prepare("UPDATE users SET updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$user['id']]);

                    // Gestion du "Se souvenir de moi"
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 jours
                        
                        // Stocker le token en base (vous pouvez créer une table remember_tokens)
                        // Pour simplifier, on utilise une approche basique ici
                    }

                    $response['success'] = true;
                    $response['message'] = "Connexion réussie ! Redirection en cours...";
                    
                    // Redirection selon le rôle
                    switch ($user['role']) {
                        case 'admin':
                            $response['redirect'] = 'admin-dashboard.php';
                            break;
                        case 'teacher':
                            $response['redirect'] = 'teacher-dashboard.php';
                            break;
                        default:
                            $response['redirect'] = 'dashboard.php';
                    }
                }
            } else {
                $response['message'] = "Nom d'utilisateur/email ou mot de passe incorrect.";
            }
        } catch (PDOException $e) {
            $response['message'] = "Erreur de connexion à la base de données.";
            error_log("Erreur de connexion: " . $e->getMessage());
        }
    }
}

// Si c'est une requête AJAX, retourner JSON
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Sinon, redirection classique
if ($response['success']) {
    header('Location: ' . $response['redirect']);
} else {
    $_SESSION['login_error'] = $response['message'];
    header('Location: login.php');
}
exit;
?>
