<?php
require_once 'config.php';

echo "<h1>🔧 Création des comptes de test</h1>";

try {
    // Vider la table users si elle existe
    $pdo->exec("DELETE FROM users");
    echo "<p style='color: orange;'>🗑️ Table users vidée</p>";
    
    // Créer les comptes avec des mots de passe hashés
    $password_hash = password_hash('password', PASSWORD_DEFAULT);
    
    $comptes = [
        // ADMINISTRATEUR
        [
            'nom' => 'Admin',
            'prenom' => 'Système',
            'email' => '<EMAIL>',
            'telephone' => '0522123456',
            'username' => 'admin',
            'password' => $password_hash,
            'role' => 'admin',
            'status' => 'active',
            'biographie' => 'Compte administrateur du système'
        ],
        
        // ÉTUDIANTS
        [
            'nom' => 'Alaoui',
            'prenom' => 'Mohammed',
            'email' => '<EMAIL>',
            'telephone' => '0612345678',
            'date_naissance' => '1998-06-15',
            'adresse' => '123 Avenue Mohammed V, Casablanca',
            'formation_souhaitee' => 'maintenance',
            'niveau_etude' => 'bac',
            'username' => 'mohammed.alaoui',
            'password' => $password_hash,
            'role' => 'student',
            'status' => 'active',
            'biographie' => 'Passionné par la maintenance industrielle'
        ],
        
        [
            'nom' => 'Benali',
            'prenom' => 'Fatima',
            'email' => '<EMAIL>',
            'telephone' => '0623456789',
            'date_naissance' => '1999-03-22',
            'adresse' => '456 Rue Hassan II, Rabat',
            'formation_souhaitee' => 'automatisation',
            'niveau_etude' => 'bac+2',
            'username' => 'fatima.benali',
            'password' => $password_hash,
            'role' => 'student',
            'status' => 'active',
            'biographie' => 'Intéressée par la robotique'
        ],
        
        [
            'nom' => 'Tazi',
            'prenom' => 'Youssef',
            'email' => '<EMAIL>',
            'telephone' => '0634567890',
            'date_naissance' => '1997-11-08',
            'adresse' => '789 Boulevard Zerktouni, Casablanca',
            'formation_souhaitee' => 'electronique',
            'niveau_etude' => 'bac',
            'username' => 'youssef.tazi',
            'password' => $password_hash,
            'role' => 'student',
            'status' => 'active',
            'biographie' => 'Spécialisé en électronique de puissance'
        ],
        
        [
            'nom' => 'Idrissi',
            'prenom' => 'Aicha',
            'email' => '<EMAIL>',
            'telephone' => '0645678901',
            'date_naissance' => '1998-09-12',
            'adresse' => '321 Avenue des FAR, Fès',
            'formation_souhaitee' => 'mecanique',
            'niveau_etude' => 'bac',
            'username' => 'aicha.idrissi',
            'password' => $password_hash,
            'role' => 'student',
            'status' => 'active',
            'biographie' => 'Passionnée par la mécanique de précision'
        ],
        
        [
            'nom' => 'Benjelloun',
            'prenom' => 'Omar',
            'email' => '<EMAIL>',
            'telephone' => '0656789012',
            'date_naissance' => '1996-12-03',
            'adresse' => '654 Rue Allal Ben Abdellah, Salé',
            'formation_souhaitee' => 'soudure',
            'niveau_etude' => 'bac',
            'username' => 'omar.benjelloun',
            'password' => $password_hash,
            'role' => 'student',
            'status' => 'active',
            'biographie' => 'Expert en soudage industriel'
        ]
    ];
    
    // Insérer chaque compte
    foreach ($comptes as $compte) {
        $sql = "INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, formation_souhaitee, niveau_etude, username, password, role, status, biographie) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $compte['nom'],
            $compte['prenom'],
            $compte['email'],
            $compte['telephone'] ?? null,
            $compte['date_naissance'] ?? null,
            $compte['adresse'] ?? null,
            $compte['formation_souhaitee'] ?? null,
            $compte['niveau_etude'] ?? null,
            $compte['username'],
            $compte['password'],
            $compte['role'],
            $compte['status'],
            $compte['biographie']
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Compte créé : " . htmlspecialchars($compte['prenom'] . ' ' . $compte['nom']) . " (" . $compte['username'] . ")</p>";
        } else {
            echo "<p style='color: red;'>❌ Erreur pour : " . htmlspecialchars($compte['prenom'] . ' ' . $compte['nom']) . "</p>";
        }
    }
    
    // Vérifier les comptes créés
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $total = $stmt->fetch()['total'];
    
    echo "<hr>";
    echo "<h2 style='color: green;'>🎉 Création terminée !</h2>";
    echo "<p><strong>Total des comptes créés : $total</strong></p>";
    
    // Afficher la liste des comptes
    echo "<h3>📋 Liste des comptes :</h3>";
    $stmt = $pdo->query("SELECT nom, prenom, username, email, role, status FROM users ORDER BY role DESC, nom ASC");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Nom</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Email</th>";
    echo "<th style='padding: 10px;'>Rôle</th>";
    echo "<th style='padding: 10px;'>Statut</th>";
    echo "</tr>";
    
    foreach ($users as $user) {
        $bg_color = $user['role'] === 'admin' ? '#ffe6e6' : '#e6f3ff';
        echo "<tr style='background: $bg_color;'>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($user['prenom'] . ' ' . $user['nom']) . "</td>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . ucfirst($user['role']) . "</td>";
        echo "<td style='padding: 8px;'>" . ucfirst($user['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🔑 Informations de connexion :</h3>";
    echo "<p><strong>Tous les mots de passe sont :</strong> <code>password</code></p>";
    echo "<p><strong>Exemples de connexion :</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin :</strong> username = <code>admin</code>, password = <code>password</code></li>";
    echo "<li><strong>Étudiant :</strong> username = <code>mohammed.alaoui</code>, password = <code>password</code></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #cce5ff; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🚀 Étapes suivantes :</h3>";
    echo "<ol>";
    echo "<li><a href='login.php' style='color: #0066cc;'>Tester la connexion</a></li>";
    echo "<li><a href='register.php' style='color: #0066cc;'>Tester l'inscription</a></li>";
    echo "<li><a href='index.php' style='color: #0066cc;'>Retour à l'accueil</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>XAMPP est démarré</li>";
    echo "<li>La base de données existe</li>";
    echo "<li>La table users existe</li>";
    echo "</ul>";
    echo "<p><a href='test_database.php'>→ Tester la base de données</a></p>";
}
?>
