<?php
require_once 'config.php';
session_start();
// Vérification de la connexion
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Espace Stagiaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">Pôle Industrie CMC</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link" href="dashboard.php">Accueil</a></li>
                    <li class="nav-item"><a class="nav-link" href="formations.php">Formations</a></li>
                    <li class="nav-item"><a class="nav-link" href="actualites.php">Actualités</a></li>
                </ul>
                <div class="d-flex">
                    <a href="profile.php" class="btn btn-outline-light me-2">Mon profil</a>
                    <a href="logout.php" class="btn btn-light">Déconnexion</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <h1>Tableau de bord</h1>
        <p class="lead">Bienvenue dans votre espace personnel</p>

        <div class="row mt-4">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action active">Mon profil</a>
                    <a href="#" class="list-group-item list-group-item-action">Mes formations</a>
                    <a href="#" class="list-group-item list-group-item-action">Emploi du temps</a>
                    <a href="#" class="list-group-item list-group-item-action">Documents</a>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Emploi du temps hebdomadaire</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Jour</th>
                                    <th>Matin (08:30 - 12:30)</th>
                                    <th>Après-midi (13:30 - 17:30)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Lundi</td>
                                    <td class="bg-light">Module 1: Fondamentaux (08:30 - 12:30)</td>
                                    <td class="bg-light">TP: Électronique (13:30 - 17:30)</td>
                                </tr>
                                <tr>
                                    <td>Mardi</td>
                                    <td class="bg-light">Module 2: Automatismes (08:30 - 12:30)</td>
                                    <td class="bg-light">TP: Automatismes (13:30 - 17:30)</td>
                                </tr>
                                <tr>
                                    <td>Mercredi</td>
                                    <td class="bg-light">Module 3: Maintenance (08:30 - 12:30)</td>
                                    <td class="bg-light">Projet tutoré (13:30 - 15:30)</td>
                                </tr>
                                <tr>
                                    <td>Jeudi</td>
                                    <td class="bg-light">Module 4: Informatique (08:30 - 12:30)</td>
                                    <td class="bg-light">TP: Programmation (13:30 - 17:30)</td>
                                </tr>
                                <tr>
                                    <td>Vendredi</td>
                                    <td class="bg-light">Module 5: Communication (08:30 - 12:30)</td>
                                    <td class="bg-light">Évaluation (13:30 - 16:30)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Questions fréquentes</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                        Comment obtenir mes identifiants de connexion?
                                    </button>
                                </h2>
                                <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Contactez l'administration du centre.
                                    </div>
                                </div>
                            </div>
                            <!-- Autres questions -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <!-- Même footer que précédemment -->
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>