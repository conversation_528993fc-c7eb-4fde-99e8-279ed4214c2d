-- Script de création de la base de données pour Pôle Industrie CMC
-- Exécutez ce script dans phpMyAdmin ou votre client MySQL

-- Création de la base de données
CREATE DATABASE IF NOT EXISTS pole_industrie_cmc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pole_industrie_cmc;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse TEXT,
    formation_souhaitee VARCHAR(100),
    niveau_etude VARCHAR(50),
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'admin', 'teacher') DEFAULT 'student',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    avatar VARCHAR(255),
    biographie TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des formations
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    duree VARCHAR(50),
    niveau VARCHAR(100),
    prix DECIMAL(10,2) DEFAULT 0.00,
    places_disponibles INT DEFAULT 0,
    date_debut DATE,
    date_fin DATE,
    horaires VARCHAR(100),
    prerequis TEXT,
    objectifs TEXT,
    programme TEXT,
    icon VARCHAR(50),
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des inscriptions
CREATE TABLE IF NOT EXISTS inscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    formation_id INT NOT NULL,
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'accepted', 'rejected', 'completed') DEFAULT 'pending',
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inscription (user_id, formation_id)
);

-- Table des actualités
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    resume TEXT,
    categorie VARCHAR(50),
    auteur_id INT,
    image VARCHAR(255),
    status ENUM('published', 'draft', 'archived') DEFAULT 'draft',
    date_publication TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (auteur_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Table des notes/évaluations
CREATE TABLE IF NOT EXISTS evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    formation_id INT NOT NULL,
    module VARCHAR(100),
    note DECIMAL(4,2),
    note_max DECIMAL(4,2) DEFAULT 20.00,
    type_evaluation ENUM('controle', 'examen', 'projet', 'tp') DEFAULT 'controle',
    date_evaluation DATE,
    commentaires TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE
);

-- Table des emplois du temps
CREATE TABLE IF NOT EXISTS emploi_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT NOT NULL,
    jour_semaine ENUM('lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi') NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    matiere VARCHAR(100) NOT NULL,
    type_cours ENUM('cours', 'tp', 'td', 'projet', 'evaluation') DEFAULT 'cours',
    salle VARCHAR(50),
    formateur VARCHAR(100),
    semaine_debut DATE,
    semaine_fin DATE,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE
);

-- Table des documents
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    nom_fichier VARCHAR(255) NOT NULL,
    chemin_fichier VARCHAR(500) NOT NULL,
    type_fichier VARCHAR(50),
    taille_fichier INT,
    formation_id INT,
    user_id INT,
    type_document ENUM('cours', 'tp', 'projet', 'evaluation', 'administratif') DEFAULT 'cours',
    acces ENUM('public', 'students', 'private') DEFAULT 'students',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insertion de données de test

-- Utilisateur administrateur par défaut
INSERT INTO users (nom, prenom, email, username, password, role, status) VALUES 
('Admin', 'Système', '<EMAIL>', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active');
-- Mot de passe: password

-- Utilisateur étudiant de test
INSERT INTO users (nom, prenom, email, telephone, username, password, formation_souhaitee, niveau_etude, role, status, biographie) VALUES 
('Alaoui', 'Mohammed', '<EMAIL>', '0612345678', 'mohammed.alaoui', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'maintenance', 'bac', 'student', 'active', 'Passionné par la maintenance industrielle et les nouvelles technologies, je cherche à approfondir mes connaissances et compétences dans ce domaine.');
-- Mot de passe: password

-- Formations de base
INSERT INTO formations (titre, description, duree, niveau, places_disponibles, category, icon, objectifs, programme) VALUES 
('Maintenance industrielle', 'Formation complète en maintenance préventive et corrective des équipements industriels. Maîtrisez les techniques modernes de diagnostic et de réparation.', '12 mois', 'Technicien Spécialisé', 25, 'maintenance', 'bi-tools', 'Maîtriser les techniques de diagnostic et de dépannage des systèmes industriels', 'Module 1: Fondamentaux\nModule 2: Électricité\nModule 3: Mécanique\nModule 4: Hydraulique'),

('Automatisation et Robotique', 'Apprenez à programmer et maintenir des systèmes automatisés et robotiques. Formation pratique sur équipements industriels réels.', '12 mois', 'Technicien Spécialisé', 20, 'automatisation', 'bi-robot', 'Programmer et maintenir des systèmes automatisés', 'Module 1: Automates programmables\nModule 2: Robotique\nModule 3: Supervision\nModule 4: Réseaux industriels'),

('Électronique industrielle', 'Conception et maintenance des systèmes électroniques industriels. Développez vos compétences en électronique de puissance.', '10 mois', 'Technicien Spécialisé', 30, 'electronique', 'bi-cpu', 'Concevoir et maintenir des systèmes électroniques', 'Module 1: Électronique analogique\nModule 2: Électronique numérique\nModule 3: Microcontrôleurs\nModule 4: Électronique de puissance');

-- Actualités de base
INSERT INTO actualites (titre, contenu, resume, categorie, auteur_id, status, date_publication) VALUES 
('Inauguration du nouveau laboratoire d\'automatisation', 'Le Pôle Industrie inaugure son nouveau laboratoire équipé des dernières technologies en matière d\'automatisation industrielle. Un investissement de 2 millions de dirhams pour offrir une formation de qualité.', 'Nouveau laboratoire d\'automatisation inauguré avec un investissement de 2M DH', 'Événement', 1, 'published', '2024-03-15 10:00:00'),

('Partenariat avec le groupe industriel LAFARGE', 'Un nouveau partenariat stratégique permettra à nos stagiaires d\'accéder à des stages pratiques dans le groupe LAFARGE. Plus de 50 postes de stage disponibles.', 'Partenariat avec LAFARGE pour 50+ stages', 'Partenariat', 1, 'published', '2024-03-05 14:30:00'),

('Journée portes ouvertes le 20 avril 2024', 'Venez découvrir nos installations et rencontrer nos formateurs lors de cette journée portes ouvertes annuelle. Démonstrations pratiques et visites guidées.', 'Journée portes ouvertes avec démonstrations pratiques', 'Événement', 1, 'published', '2024-03-01 09:00:00');

-- Emploi du temps exemple pour la formation maintenance
INSERT INTO emploi_temps (formation_id, jour_semaine, heure_debut, heure_fin, matiere, type_cours, salle, formateur) VALUES 
(1, 'lundi', '08:30:00', '12:30:00', 'Fondamentaux de la maintenance', 'cours', 'A101', 'M. Bennani'),
(1, 'lundi', '13:30:00', '17:30:00', 'TP Électronique', 'tp', 'Lab1', 'M. Alami'),
(1, 'mardi', '08:30:00', '12:30:00', 'Automatismes industriels', 'cours', 'A102', 'Mme Fassi'),
(1, 'mardi', '13:30:00', '17:30:00', 'TP Automatismes', 'tp', 'Lab2', 'M. Tazi'),
(1, 'mercredi', '08:30:00', '12:30:00', 'Maintenance préventive', 'cours', 'A101', 'M. Bennani'),
(1, 'mercredi', '13:30:00', '15:30:00', 'Projet tutoré', 'projet', 'Lab3', 'M. Alami'),
(1, 'jeudi', '08:30:00', '12:30:00', 'Informatique industrielle', 'cours', 'B201', 'M. Idrissi'),
(1, 'jeudi', '13:30:00', '17:30:00', 'TP Programmation', 'tp', 'LabInfo', 'M. Idrissi'),
(1, 'vendredi', '08:30:00', '12:30:00', 'Communication professionnelle', 'cours', 'A103', 'Mme Lahlou'),
(1, 'vendredi', '13:30:00', '16:30:00', 'Évaluation hebdomadaire', 'evaluation', 'A101', 'Équipe pédagogique');
