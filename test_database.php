<?php
// Script de test pour vérifier la connexion à la base de données et créer la table users

require_once 'config.php';

echo "<h2>Test de connexion à la base de données</h2>";

try {
    // Test de connexion
    echo "<p style='color: green;'>✅ Connexion à la base de données réussie !</p>";
    
    // Vérifier si la table users existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "<p style='color: orange;'>⚠️ La table 'users' n'existe pas. Création en cours...</p>";
        
        // Créer la table users
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(100) NOT NULL,
            prenom VARCHAR(100) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            telephone VARCHAR(20),
            date_naissance DATE,
            adresse TEXT,
            formation_souhaitee VARCHAR(100),
            niveau_etude VARCHAR(50),
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('student', 'admin', 'teacher') DEFAULT 'student',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            avatar VARCHAR(255),
            biographie TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ Table 'users' créée avec succès !</p>";
        
        // Créer un utilisateur admin par défaut
        $admin_password = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (nom, prenom, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Admin', 'Système', '<EMAIL>', 'admin', $admin_password, 'admin', 'active']);
        echo "<p style='color: green;'>✅ Utilisateur admin créé (username: admin, password: password)</p>";
        
    } else {
        echo "<p style='color: green;'>✅ La table 'users' existe déjà.</p>";
    }
    
    // Afficher la structure de la table
    echo "<h3>Structure de la table users :</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Compter les utilisateurs existants
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $count = $stmt->fetch()['total'];
    echo "<p><strong>Nombre d'utilisateurs dans la base : $count</strong></p>";
    
    // Afficher les utilisateurs existants
    if ($count > 0) {
        echo "<h3>Utilisateurs existants :</h3>";
        $stmt = $pdo->query("SELECT id, nom, prenom, email, username, role, status, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll();

        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nom</th><th>Prénom</th><th>Email</th><th>Username</th><th>Rôle</th><th>Statut</th><th>Créé le</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['nom']) . "</td>";
            echo "<td>" . htmlspecialchars($user['prenom']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Test de connexion avec un compte
        echo "<h3>Test de connexion :</h3>";
        echo "<form method='POST' style='background: #f0f0f0; padding: 20px; margin: 20px 0;'>";
        echo "<h4>Tester la connexion d'un compte :</h4>";
        echo "<label>Username ou Email :</label><br>";
        echo "<input type='text' name='test_username' value='admin' style='width: 200px; padding: 5px; margin: 5px 0;'><br>";
        echo "<label>Mot de passe :</label><br>";
        echo "<input type='password' name='test_password' value='password' style='width: 200px; padding: 5px; margin: 5px 0;'><br>";
        echo "<input type='submit' name='test_login' value='Tester la connexion' style='padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; cursor: pointer;'>";
        echo "</form>";

        // Traitement du test de connexion
        if (isset($_POST['test_login'])) {
            $test_username = $_POST['test_username'];
            $test_password = $_POST['test_password'];

            echo "<div style='background: #e9ecef; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff;'>";
            echo "<h4>Résultat du test de connexion :</h4>";

            try {
                $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$test_username, $test_username]);
                $user = $stmt->fetch();

                if ($user) {
                    echo "<p style='color: green;'>✅ Utilisateur trouvé : " . htmlspecialchars($user['prenom'] . ' ' . $user['nom']) . "</p>";
                    echo "<p>Email : " . htmlspecialchars($user['email']) . "</p>";
                    echo "<p>Username : " . htmlspecialchars($user['username']) . "</p>";
                    echo "<p>Rôle : " . $user['role'] . "</p>";
                    echo "<p>Statut : " . $user['status'] . "</p>";

                    if (password_verify($test_password, $user['password'])) {
                        echo "<p style='color: green; font-weight: bold;'>✅ MOT DE PASSE CORRECT !</p>";
                        echo "<p>La connexion devrait fonctionner sur la page login.php</p>";
                    } else {
                        echo "<p style='color: red; font-weight: bold;'>❌ MOT DE PASSE INCORRECT !</p>";
                        echo "<p>Le mot de passe dans la base ne correspond pas.</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Utilisateur non trouvé avec ce username/email</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
            }
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Aucun utilisateur dans la base de données.</p>";
        echo "<p><strong>Solution :</strong> Importez le fichier base_donnees_complete.sql dans phpMyAdmin</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    echo "<p>Vérifiez que :</p>";
    echo "<ul>";
    echo "<li>XAMPP est démarré (Apache et MySQL)</li>";
    echo "<li>La base de données 'pole_industrie_cmc' existe</li>";
    echo "<li>Les paramètres dans config.php sont corrects</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>Instructions :</h3>";
echo "<ol>";
echo "<li>Si vous voyez des erreurs, corrigez-les avant de continuer</li>";
echo "<li>Si tout est OK, vous pouvez tester l'inscription sur <a href='register.php'>register.php</a></li>";
echo "<li>Puis tester la connexion sur <a href='login.php'>login.php</a></li>";
echo "</ol>";

echo "<p><a href='index.php'>← Retour à l'accueil</a></p>";
?>
