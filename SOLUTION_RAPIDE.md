# 🚨 SOLUTION RAPIDE - Problème de connexion

## 🎯 Problème identifié
- Les comptes étudiants ne se créent pas
- La connexion ne fonctionne pas

## ⚡ Solution en 3 étapes

### Étape 1: Vérifier XAMPP
1. Ouvrir XAMPP Control Panel
2. S'assurer qu'**Apache** et **MySQL** sont démarrés (voyants verts)
3. Si pas démarrés, cliquer sur "Start"

### Étape 2: Créer les comptes automatiquement
1. Aller sur : `http://localhost/soutnance/creer_comptes.php`
2. Cette page va automatiquement créer tous les comptes de test
3. Vérifier que les comptes sont créés avec succès

### Étape 3: Tester la connexion
1. Aller sur : `http://localhost/soutnance/login.php`
2. Essayer de se connecter avec :
   - **Username:** `admin`
   - **Password:** `password`

---

## 🔧 Si ça ne marche toujours pas

### Diagnostic complet
Aller sur : `http://localhost/soutnance/debug_connexion.php`

Cette page va :
- ✅ Vérifier la base de données
- ✅ Lister tous les comptes
- ✅ Tester une connexion en direct
- ✅ Identifier le problème exact

---

## 🎯 Comptes de test créés

### Administrateur
- **Username:** `admin`
- **Password:** `password`
- **Accès:** Dashboard admin

### Étudiants
- **Username:** `mohammed.alaoui` | **Password:** `password`
- **Username:** `fatima.benali` | **Password:** `password`
- **Username:** `youssef.tazi` | **Password:** `password`
- **Username:** `aicha.idrissi` | **Password:** `password`
- **Username:** `omar.benjelloun` | **Password:** `password`

---

## 🚀 Test rapide

1. **Créer les comptes :** `http://localhost/soutnance/creer_comptes.php`
2. **Tester connexion :** `http://localhost/soutnance/login.php`
3. **Diagnostic :** `http://localhost/soutnance/debug_connexion.php`

---

## 📞 Dépannage

### Erreur "Base de données introuvable"
→ Aller sur `http://localhost/soutnance/test_database.php`

### Erreur "Table users n'existe pas"
→ Aller sur `http://localhost/soutnance/creer_comptes.php`

### Page blanche
→ Vérifier que XAMPP est démarré

### Mot de passe incorrect
→ Tous les mots de passe sont `password`

---

## ✅ Vérification finale

Après avoir suivi les étapes :
1. ✅ XAMPP démarré
2. ✅ Comptes créés via `creer_comptes.php`
3. ✅ Connexion testée sur `login.php`
4. ✅ Admin peut accéder au dashboard
5. ✅ Étudiants peuvent se connecter

Si tout est ✅, le système fonctionne parfaitement !
