<?php
require_once 'config.php';

echo "<h1>🔍 Diagnostic Complet - Problème de Connexion</h1>";

// Test 1: Vérifier la connexion à la base de données
echo "<h2>1. Test de connexion à la base de données</h2>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ Connexion à la base de données OK</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur de connexion : " . $e->getMessage() . "</p>";
    echo "<p><strong>Solution :</strong> Vérifiez que XAMPP est démarré et que MySQL fonctionne.</p>";
    exit;
}

// Test 2: Vérifier l'existence de la table users
echo "<h2>2. Vérification de la table users</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->fetch()) {
        echo "<p style='color: green;'>✅ Table 'users' existe</p>";
        
        // Vérifier la structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        echo "<p>Colonnes disponibles : " . implode(', ', array_column($columns, 'Field')) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Table 'users' n'existe pas</p>";
        echo "<p><strong>Solution :</strong> <a href='test_database.php'>Créer la table users</a></p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    exit;
}

// Test 3: Compter les utilisateurs
echo "<h2>3. Vérification des utilisateurs existants</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $total = $stmt->fetch()['total'];
    
    if ($total == 0) {
        echo "<p style='color: red;'>❌ Aucun utilisateur dans la base de données</p>";
        echo "<p><strong>Solution :</strong> <a href='creer_comptes.php'>Créer les comptes de test</a></p>";
        
        // Créer un compte admin rapidement
        echo "<h3>Création d'un compte admin de test :</h3>";
        $admin_password = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (nom, prenom, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        if ($stmt->execute(['Admin', 'Test', '<EMAIL>', 'admin', $admin_password, 'admin', 'active'])) {
            echo "<p style='color: green;'>✅ Compte admin créé : username = 'admin', password = 'password'</p>";
        }
        
        // Créer un compte étudiant
        $student_password = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (nom, prenom, email, username, password, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        if ($stmt->execute(['Etudiant', 'Test', '<EMAIL>', 'etudiant', $student_password, 'student', 'active'])) {
            echo "<p style='color: green;'>✅ Compte étudiant créé : username = 'etudiant', password = 'password'</p>";
        }
        
        $total = 2; // Mise à jour du total
    } else {
        echo "<p style='color: green;'>✅ $total utilisateur(s) trouvé(s)</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
}

// Test 4: Lister tous les utilisateurs
echo "<h2>4. Liste de tous les utilisateurs</h2>";
try {
    $stmt = $pdo->query("SELECT id, nom, prenom, email, username, role, status, created_at FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Nom</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Rôle</th>";
        echo "<th style='padding: 8px;'>Statut</th>";
        echo "<th style='padding: 8px;'>Créé le</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            $bg = $user['status'] === 'active' ? '#d4edda' : '#f8d7da';
            echo "<tr style='background: $bg;'>";
            echo "<td style='padding: 8px;'>" . $user['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($user['prenom'] . ' ' . $user['nom']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . $user['role'] . "</td>";
            echo "<td style='padding: 8px;'>" . $user['status'] . "</td>";
            echo "<td style='padding: 8px;'>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
}

// Test 5: Test de connexion en direct
echo "<h2>5. Test de connexion en direct</h2>";

if (isset($_POST['test_login'])) {
    $test_username = trim($_POST['test_username']);
    $test_password = $_POST['test_password'];
    
    echo "<div style='background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>Résultat du test avec : " . htmlspecialchars($test_username) . "</h3>";
    
    try {
        // Étape 1: Rechercher l'utilisateur
        echo "<p><strong>Étape 1 :</strong> Recherche de l'utilisateur...</p>";
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$test_username, $test_username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo "<p style='color: red;'>❌ Utilisateur non trouvé avec ce username/email</p>";
            echo "<p>Vérifiez l'orthographe ou utilisez un des comptes listés ci-dessus.</p>";
        } else {
            echo "<p style='color: green;'>✅ Utilisateur trouvé :</p>";
            echo "<ul>";
            echo "<li>ID : " . $user['id'] . "</li>";
            echo "<li>Nom : " . htmlspecialchars($user['prenom'] . ' ' . $user['nom']) . "</li>";
            echo "<li>Email : " . htmlspecialchars($user['email']) . "</li>";
            echo "<li>Username : " . htmlspecialchars($user['username']) . "</li>";
            echo "<li>Rôle : " . $user['role'] . "</li>";
            echo "<li>Statut : " . $user['status'] . "</li>";
            echo "</ul>";
            
            // Étape 2: Vérifier le statut
            echo "<p><strong>Étape 2 :</strong> Vérification du statut...</p>";
            if ($user['status'] !== 'active') {
                echo "<p style='color: orange;'>⚠️ Compte non actif (statut: " . $user['status'] . ")</p>";
                echo "<p>Ce compte ne peut pas se connecter car il n'est pas actif.</p>";
            } else {
                echo "<p style='color: green;'>✅ Compte actif</p>";
                
                // Étape 3: Vérifier le mot de passe
                echo "<p><strong>Étape 3 :</strong> Vérification du mot de passe...</p>";
                if (password_verify($test_password, $user['password'])) {
                    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 CONNEXION RÉUSSIE !</p>";
                    echo "<p>Le mot de passe est correct. La connexion devrait fonctionner sur login.php</p>";
                    
                    // Afficher où l'utilisateur sera redirigé
                    if ($user['role'] === 'admin') {
                        echo "<p>→ Redirection vers : <a href='admin-dashboard.php'>Dashboard Admin</a></p>";
                    } else {
                        echo "<p>→ Redirection vers : <a href='index.php'>Page d'accueil</a></p>";
                    }
                } else {
                    echo "<p style='color: red; font-weight: bold;'>❌ MOT DE PASSE INCORRECT</p>";
                    echo "<p>Le mot de passe fourni ne correspond pas au hash stocké en base.</p>";
                    
                    // Afficher le hash pour debug
                    echo "<p><small>Hash en base : " . substr($user['password'], 0, 50) . "...</small></p>";
                    
                    // Proposer de réinitialiser le mot de passe
                    echo "<p><strong>Solution :</strong> Réinitialiser le mot de passe :</p>";
                    echo "<form method='POST' style='display: inline;'>";
                    echo "<input type='hidden' name='reset_password' value='1'>";
                    echo "<input type='hidden' name='user_id' value='" . $user['id'] . "'>";
                    echo "<input type='submit' value='Réinitialiser le mot de passe à \"password\"' style='background: #dc3545; color: white; padding: 8px 15px; border: none; border-radius: 3px;'>";
                    echo "</form>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}

// Traitement de la réinitialisation de mot de passe
if (isset($_POST['reset_password'])) {
    $user_id = $_POST['user_id'];
    $new_password = password_hash('password', PASSWORD_DEFAULT);
    
    try {
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        if ($stmt->execute([$new_password, $user_id])) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p style='color: green; font-weight: bold;'>✅ Mot de passe réinitialisé à 'password'</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erreur de réinitialisation : " . $e->getMessage() . "</p>";
    }
}

// Formulaire de test
echo "<div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>🧪 Tester une connexion :</h3>";
echo "<form method='POST'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Username ou Email :</label><br>";
echo "<input type='text' name='test_username' value='admin' style='width: 300px; padding: 8px; margin: 5px 0;'>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Mot de passe :</label><br>";
echo "<input type='password' name='test_password' value='password' style='width: 300px; padding: 8px; margin: 5px 0;'>";
echo "</div>";
echo "<div style='margin: 15px 0;'>";
echo "<input type='submit' name='test_login' value='Tester la connexion' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer;'>";
echo "</div>";
echo "</form>";
echo "</div>";

// Actions recommandées
echo "<h2>6. Actions recommandées</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Si aucun utilisateur n'existe :</h3>";
echo "<p><a href='creer_comptes.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Créer les comptes de test</a></p>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>Si les utilisateurs existent mais la connexion échoue :</h3>";
echo "<ol>";
echo "<li>Utilisez le formulaire de test ci-dessus</li>";
echo "<li>Vérifiez que le statut est 'active'</li>";
echo "<li>Réinitialisez le mot de passe si nécessaire</li>";
echo "<li>Testez sur <a href='login.php'>login.php</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='index.php'>← Retour à l'accueil</a></p>";
?>
