<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nos Formations - Pôle Industrie CMC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-gear-fill me-2"></i>Pôle Industrie CMC
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-house me-1"></i>Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="formations.php">
                            <i class="bi bi-book me-1"></i>Formations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="actualites.php">
                            <i class="bi bi-newspaper me-1"></i>Actualités
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="bi bi-person me-1"></i>Connexion
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section pour les formations -->
    <section class="bg-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Nos Formations</h1>
                    <p class="lead fs-5">
                        Découvrez nos formations professionnelles conçues pour répondre aux besoins de l'industrie moderne.
                        Développez vos compétences avec nos programmes certifiants.
                    </p>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="hero-stats">
                        <div class="stat-item bg-white bg-opacity-10 rounded-3 p-3 mb-3">
                            <h3 class="fw-bold text-warning mb-1">12+</h3>
                            <small>Formations disponibles</small>
                        </div>
                        <div class="stat-item bg-white bg-opacity-10 rounded-3 p-3">
                            <h3 class="fw-bold text-warning mb-1">6-18</h3>
                            <small>Mois de formation</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container my-5">
        <!-- Filtres de recherche -->
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white border-0">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control border-0" placeholder="Rechercher une formation..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select border-0 bg-light" id="categoryFilter">
                                    <option value="">Toutes les catégories</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="automatisation">Automatisation</option>
                                    <option value="electronique">Électronique</option>
                                    <option value="mecanique">Mécanique</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="filterFormations()">
                                    <i class="bi bi-funnel me-1"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grille des formations -->
        <div class="row g-4" id="formationsGrid">
            <?php
            require_once 'config.php';
            $formations = [
                [
                    'titre' => 'Maintenance industrielle',
                    'description' => 'Formation complète en maintenance préventive et corrective des équipements industriels. Maîtrisez les techniques modernes de diagnostic et de réparation.',
                    'duree' => '12 mois',
                    'niveau' => 'Technicien Spécialisé',
                    'icon' => 'bi-tools',
                    'category' => 'maintenance',
                    'prix' => 'Gratuit',
                    'places' => '25 places'
                ],
                [
                    'titre' => 'Automatisation et Robotique',
                    'description' => 'Apprenez à programmer et maintenir des systèmes automatisés et robotiques. Formation pratique sur équipements industriels réels.',
                    'duree' => '12 mois',
                    'niveau' => 'Technicien Spécialisé',
                    'icon' => 'bi-robot',
                    'category' => 'automatisation',
                    'prix' => 'Gratuit',
                    'places' => '20 places'
                ],
                [
                    'titre' => 'Électronique industrielle',
                    'description' => 'Conception et maintenance des systèmes électroniques industriels. Développez vos compétences en électronique de puissance.',
                    'duree' => '10 mois',
                    'niveau' => 'Technicien Spécialisé',
                    'icon' => 'bi-cpu',
                    'category' => 'electronique',
                    'prix' => 'Gratuit',
                    'places' => '30 places'
                ],
                [
                    'titre' => 'Mécanique industrielle',
                    'description' => 'Formation en usinage, assemblage et maintenance mécanique. Maîtrisez les machines-outils et les techniques d\'usinage.',
                    'duree' => '8 mois',
                    'niveau' => 'Technicien',
                    'icon' => 'bi-gear',
                    'category' => 'mecanique',
                    'prix' => 'Gratuit',
                    'places' => '35 places'
                ],
                [
                    'titre' => 'Soudure industrielle',
                    'description' => 'Techniques de soudage avancées pour l\'industrie. Formation certifiante aux normes internationales.',
                    'duree' => '6 mois',
                    'niveau' => 'Qualification',
                    'icon' => 'bi-fire',
                    'category' => 'mecanique',
                    'prix' => 'Gratuit',
                    'places' => '15 places'
                ],
                [
                    'titre' => 'Contrôle qualité',
                    'description' => 'Méthodes et outils de contrôle qualité industriel. Apprenez les normes ISO et les techniques de mesure.',
                    'duree' => '9 mois',
                    'niveau' => 'Technicien',
                    'icon' => 'bi-check-circle',
                    'category' => 'maintenance',
                    'prix' => 'Gratuit',
                    'places' => '25 places'
                ]
            ];

            foreach ($formations as $index => $formation) {
                echo '<div class="col-md-6 col-lg-4 formation-card" data-category="' . $formation['category'] . '">';
                echo '<div class="card h-100 shadow-sm border-0">';
                echo '<div class="card-header bg-primary text-white text-center py-3">';
                echo '<i class="bi ' . $formation['icon'] . ' display-4 mb-2"></i>';
                echo '<h5 class="card-title mb-0 fw-bold">' . htmlspecialchars($formation['titre']) . '</h5>';
                echo '</div>';
                echo '<div class="card-body d-flex flex-column">';
                echo '<p class="card-text flex-grow-1">' . htmlspecialchars($formation['description']) . '</p>';
                echo '<div class="formation-info mb-3">';
                echo '<div class="row g-2 text-center">';
                echo '<div class="col-6">';
                echo '<div class="bg-light rounded p-2">';
                echo '<i class="bi bi-clock text-primary"></i>';
                echo '<div class="small fw-bold">' . htmlspecialchars($formation['duree']) . '</div>';
                echo '</div></div>';
                echo '<div class="col-6">';
                echo '<div class="bg-light rounded p-2">';
                echo '<i class="bi bi-people text-success"></i>';
                echo '<div class="small fw-bold">' . htmlspecialchars($formation['places']) . '</div>';
                echo '</div></div>';
                echo '</div></div>';
                echo '<div class="d-flex justify-content-between align-items-center mb-3">';
                echo '<span class="badge bg-primary fs-6">' . htmlspecialchars($formation['niveau']) . '</span>';
                echo '<span class="text-success fw-bold">' . htmlspecialchars($formation['prix']) . '</span>';
                echo '</div>';
                echo '<a href="formation-detail.php?id=' . $index . '" class="btn btn-primary w-100">';
                echo '<i class="bi bi-arrow-right me-2"></i>En savoir plus';
                echo '</a>';
                echo '</div></div></div>';
            }
            ?>
        </div>

        <div class="mt-5">
            <h2>Comment s'inscrire ?</h2>
            <div class="row g-4 mt-3">
                <div class="col-md-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="display-4 mb-3">1</div>
                            <h5>Choisir une formation</h5>
                            <p>Sélectionnez la formation qui correspond à vos objectifs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="display-4 mb-3">2</div>
                            <h5>Vérifier l'éligibilité</h5>
                            <p>Vérifiez les prérequis et assurez-vous d'être éligible</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="display-4 mb-3">3</div>
                            <h5>S'inscrire en ligne</h5>
                            <p>Remplissez le formulaire et joignez les documents requis</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="display-4 mb-3">4</div>
                            <h5>Entretien</h5>
                            <p>Passez un entretien avec nos formateurs pour valider votre candidature</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-gear-fill me-2 text-warning"></i>Pôle Industrie CMC
                    </h5>
                    <p class="mb-3">
                        Centre d'excellence pour la formation industrielle, nous préparons les professionnels de demain
                        avec des formations de qualité adaptées aux besoins du marché.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-light me-3"><i class="bi bi-linkedin fs-4"></i></a>
                        <a href="#" class="text-light"><i class="bi bi-youtube fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Formations</h6>
                    <ul class="list-unstyled">
                        <li><a href="formations.php" class="text-light-emphasis text-decoration-none">Maintenance industrielle</a></li>
                        <li><a href="formations.php" class="text-light-emphasis text-decoration-none">Automatisation</a></li>
                        <li><a href="formations.php" class="text-light-emphasis text-decoration-none">Électronique</a></li>
                        <li><a href="formations.php" class="text-light-emphasis text-decoration-none">Toutes les formations</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Liens utiles</h6>
                    <ul class="list-unstyled">
                        <li><a href="actualites.php" class="text-light-emphasis text-decoration-none">Actualités</a></li>
                        <li><a href="dashboard.php" class="text-light-emphasis text-decoration-none">Dashboard</a></li>
                        <li><a href="profile.php" class="text-light-emphasis text-decoration-none">Mon profil</a></li>
                        <li><a href="login.php" class="text-light-emphasis text-decoration-none">Connexion</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact</h6>
                    <div class="contact-info">
                        <p class="mb-2">
                            <i class="bi bi-geo-alt me-2 text-warning"></i>
                            123 Avenue de l'Industrie, Casablanca, Maroc
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-telephone me-2 text-warning"></i>
                            +212 5 22 XX XX XX
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-envelope me-2 text-warning"></i>
                            <EMAIL>
                        </p>
                        <p class="mb-0">
                            <i class="bi bi-clock me-2 text-warning"></i>
                            Lun - Ven: 8h00 - 18h00
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 Pôle Industrie CMC. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light-emphasis text-decoration-none me-3">Politique de confidentialité</a>
                    <a href="#" class="text-light-emphasis text-decoration-none">Conditions d'utilisation</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Fonction de filtrage des formations
        function filterFormations() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const category = document.getElementById('categoryFilter').value;
            const cards = document.querySelectorAll('.formation-card');

            cards.forEach(card => {
                const title = card.querySelector('.card-title').textContent.toLowerCase();
                const description = card.querySelector('.card-text').textContent.toLowerCase();
                const cardCategory = card.getAttribute('data-category');

                const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
                const matchesCategory = category === '' || cardCategory === category;

                if (matchesSearch && matchesCategory) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Filtrage en temps réel
        document.getElementById('searchInput').addEventListener('input', filterFormations);
        document.getElementById('categoryFilter').addEventListener('change', filterFormations);

        // Animation au scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.formation-card').forEach(card => {
            card.classList.add('animate-on-scroll');
            observer.observe(card);
        });
    </script>
</body>
</html>