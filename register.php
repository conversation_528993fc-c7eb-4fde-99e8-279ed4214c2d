<?php
require_once 'config.php';
session_start();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = trim($_POST['nom']);
    $prenom = trim($_POST['prenom']);
    $email = trim($_POST['email']);
    $telephone = trim($_POST['telephone']);
    $date_naissance = $_POST['date_naissance'];
    $adresse = trim($_POST['adresse']);
    $formation_souhaitee = $_POST['formation_souhaitee'];
    $niveau_etude = $_POST['niveau_etude'];
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validation
    if (empty($nom) || empty($prenom) || empty($email) || empty($username) || empty($password)) {
        $error = "Tous les champs obligatoires doivent être remplis.";
    } elseif ($password !== $confirm_password) {
        $error = "Les mots de passe ne correspondent pas.";
    } elseif (strlen($password) < 6) {
        $error = "Le mot de passe doit contenir au moins 6 caractères.";
    } else {
        try {
            // Vérifier si l'email ou le nom d'utilisateur existe déjà
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
            $stmt->execute([$email, $username]);
            
            if ($stmt->fetch()) {
                $error = "Cet email ou nom d'utilisateur est déjà utilisé.";
            } else {
                // Créer le compte
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (nom, prenom, email, telephone, date_naissance, adresse, 
                                     formation_souhaitee, niveau_etude, username, password, role, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'student', NOW())
                ");
                
                if ($stmt->execute([$nom, $prenom, $email, $telephone, $date_naissance,
                                  $adresse, $formation_souhaitee, $niveau_etude, $username, $hashed_password])) {
                    // Redirection vers la page de connexion avec message de succès
                    header('Location: login.php?message=registered');
                    exit;
                } else {
                    $error = "Erreur lors de la création du compte.";
                }
            }
        } catch (PDOException $e) {
            $error = "Erreur de base de données : " . $e->getMessage();
            // Log l'erreur pour le débogage
            error_log("Erreur inscription: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Pôle Industrie CMC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-background">
        <div class="login-overlay"></div>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark bg-transparent position-absolute w-100" style="z-index: 1000;">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-gear-fill me-2"></i>Pôle Industrie CMC
            </a>
            <div class="d-flex">
                <a href="index.php" class="btn btn-outline-light me-2">
                    <i class="bi bi-house me-1"></i>Accueil
                </a>
                <a href="login.php" class="btn btn-outline-light">
                    <i class="bi bi-box-arrow-in-right me-1"></i>Connexion
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center position-relative" style="z-index: 100; padding-top: 100px; padding-bottom: 50px;">
        <div class="row justify-content-center w-100">
            <div class="col-md-10 col-lg-8 col-xl-6">
                <div class="login-card card border-0 shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <i class="bi bi-person-plus-fill display-4 mb-3"></i>
                        <h2 class="mb-0 fw-bold">Inscription</h2>
                        <p class="mb-0 opacity-75">Créer votre compte étudiant</p>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" id="registerForm">
                            <!-- Informations personnelles -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="nom" class="form-label fw-semibold">
                                        <i class="bi bi-person me-2 text-primary"></i>Nom *
                                    </label>
                                    <input type="text" class="form-control" id="nom" name="nom" required 
                                           value="<?php echo isset($_POST['nom']) ? htmlspecialchars($_POST['nom']) : ''; ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="prenom" class="form-label fw-semibold">
                                        <i class="bi bi-person me-2 text-primary"></i>Prénom *
                                    </label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" required
                                           value="<?php echo isset($_POST['prenom']) ? htmlspecialchars($_POST['prenom']) : ''; ?>">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="bi bi-envelope me-2 text-primary"></i>Email *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="telephone" class="form-label fw-semibold">
                                        <i class="bi bi-telephone me-2 text-primary"></i>Téléphone
                                    </label>
                                    <input type="tel" class="form-control" id="telephone" name="telephone"
                                           value="<?php echo isset($_POST['telephone']) ? htmlspecialchars($_POST['telephone']) : ''; ?>">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="date_naissance" class="form-label fw-semibold">
                                        <i class="bi bi-calendar me-2 text-primary"></i>Date de naissance
                                    </label>
                                    <input type="date" class="form-control" id="date_naissance" name="date_naissance"
                                           value="<?php echo isset($_POST['date_naissance']) ? $_POST['date_naissance'] : ''; ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="niveau_etude" class="form-label fw-semibold">
                                        <i class="bi bi-mortarboard me-2 text-primary"></i>Niveau d'étude
                                    </label>
                                    <select class="form-select" id="niveau_etude" name="niveau_etude">
                                        <option value="">Sélectionner...</option>
                                        <option value="bac" <?php echo (isset($_POST['niveau_etude']) && $_POST['niveau_etude'] == 'bac') ? 'selected' : ''; ?>>Baccalauréat</option>
                                        <option value="bac+2" <?php echo (isset($_POST['niveau_etude']) && $_POST['niveau_etude'] == 'bac+2') ? 'selected' : ''; ?>>Bac+2</option>
                                        <option value="bac+3" <?php echo (isset($_POST['niveau_etude']) && $_POST['niveau_etude'] == 'bac+3') ? 'selected' : ''; ?>>Bac+3</option>
                                        <option value="autre" <?php echo (isset($_POST['niveau_etude']) && $_POST['niveau_etude'] == 'autre') ? 'selected' : ''; ?>>Autre</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="adresse" class="form-label fw-semibold">
                                    <i class="bi bi-geo-alt me-2 text-primary"></i>Adresse
                                </label>
                                <textarea class="form-control" id="adresse" name="adresse" rows="2"><?php echo isset($_POST['adresse']) ? htmlspecialchars($_POST['adresse']) : ''; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="formation_souhaitee" class="form-label fw-semibold">
                                    <i class="bi bi-book me-2 text-primary"></i>Formation souhaitée
                                </label>
                                <select class="form-select" id="formation_souhaitee" name="formation_souhaitee">
                                    <option value="">Sélectionner une formation...</option>
                                    <option value="maintenance" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'maintenance') ? 'selected' : ''; ?>>Maintenance industrielle</option>
                                    <option value="automatisation" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'automatisation') ? 'selected' : ''; ?>>Automatisation et Robotique</option>
                                    <option value="electronique" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'electronique') ? 'selected' : ''; ?>>Électronique industrielle</option>
                                    <option value="mecanique" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'mecanique') ? 'selected' : ''; ?>>Mécanique industrielle</option>
                                    <option value="soudure" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'soudure') ? 'selected' : ''; ?>>Soudure industrielle</option>
                                    <option value="qualite" <?php echo (isset($_POST['formation_souhaitee']) && $_POST['formation_souhaitee'] == 'qualite') ? 'selected' : ''; ?>>Contrôle qualité</option>
                                </select>
                            </div>

                            <hr class="my-4">

                            <!-- Informations de connexion -->
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-key me-2"></i>Informations de connexion
                            </h5>

                            <div class="mb-3">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person-badge me-2 text-primary"></i>Nom d'utilisateur *
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                                <div class="form-text">Choisissez un nom d'utilisateur unique</div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="password" class="form-label fw-semibold">
                                        <i class="bi bi-lock me-2 text-primary"></i>Mot de passe *
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">Minimum 6 caractères</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="confirm_password" class="form-label fw-semibold">
                                        <i class="bi bi-lock-fill me-2 text-primary"></i>Confirmer le mot de passe *
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        J'accepte les <a href="#" class="text-primary">conditions d'utilisation</a> et la <a href="#" class="text-primary">politique de confidentialité</a>
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 py-3 fw-semibold">
                                <i class="bi bi-person-plus me-2"></i>Créer mon compte
                            </button>
                        </form>

                        <div class="text-center mt-4 pt-4 border-top">
                            <p class="text-muted mb-2">Vous avez déjà un compte ?</p>
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Se connecter
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Validation du formulaire
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Les mots de passe ne correspondent pas.');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Le mot de passe doit contenir au moins 6 caractères.');
                return false;
            }
        });

        // Vérification en temps réel des mots de passe
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Les mots de passe ne correspondent pas');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });
    </script>
</body>
</html>
